# 数据库字典文档

## 概述
本文档详细说明了电站人员调度管理系统中各个数据库表的字段结构和说明。

### 数据库信息
- **数据库名称**: `power_station_management`
- **数据库类型**: MongoDB
- **连接地址**: `mongodb://localhost:27017/power_station_management`
- **字符编码**: UTF-8

### 业务背景
电站人员调度管理系统是一个用于管理电力企业人员调度、岗位分配和电站运营的综合管理平台。系统支持多电站、多岗位的复杂人员管理需求，包括主岗位分配、兼职岗位管理、人员调度审批等核心业务功能。

## 1. 员工表 (employees)

### 基础字段

| 字段名 | 类型 | 必填 | 业务含义 | 说明 | 示例 |
|--------|------|------|----------|------|------|
| `_id` | ObjectId | 是 | 员工唯一标识 | MongoDB自动生成的唯一标识符，用于数据库内部关联 | `6879de4236b67dd911c11d15` |
| `name` | String | 是 | 员工姓名 | 员工的真实姓名，用于显示和查询 | `杨俊奇` |
| `homeStationId` | ObjectId | 是 | 归属电站 | 员工编制所属的电站，决定员工的基本工作地点和管理归属 | `6879de4236b67dd911c11d0b` |
| `currentStationId` | ObjectId | 是 | 当前工作电站 | 员工当前实际工作的电站，可能因调度而与归属电站不同 | `6879de4236b67dd911c11d0b` |
| `position` | String | 是 | 职位名称 | 员工的职位或岗位名称，用于权限控制和工作分配 | `项目经理`、`站长`、`运维工程师` |
| `department` | String | 是 | 所属部门 | 员工所属的业务部门，用于组织架构管理 | `management`、`operations`、`maintenance` |
| `employeeType` | String | 是 | 员工类型 | 员工的雇佣类型，影响管理方式和权限范围 | `full_time`、`part_time`、`intern`、`external`、`borrowed` |
| `status` | String | 是 | 员工状态 | 员工当前的工作状态，用于人员调度和管理 | `active`、`learning`、`inactive`、`vacant` |
| `hireDate` | Date | 是 | 入职日期 | 员工正式入职的日期，用于工龄计算和统计 | `2019-12-31T16:00:00.000Z` |
| `skills` | Array[String] | 否 | 技能清单 | 员工掌握的专业技能，用于岗位匹配和调度决策 | `["电站管理", "安全监督"]` |
| `certifications` | Array[String] | 否 | 资质证书 | 员工持有的专业证书，用于岗位资格验证 | `["电力工程师", "安全工程师"]` |
| `phone` | String | 否 | 联系电话 | 员工的联系方式，用于紧急联系和通知 | `13800138000` |
| `email` | String | 否 | 电子邮箱 | 员工的邮箱地址，用于系统通知和文档发送 | `<EMAIL>` |
| `notes` | String | 否 | 备注信息 | 员工的额外信息或特殊说明 | `经验丰富的项目经理` |

### 系统字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `createdAt` | Date | 记录创建时间 |
| `updatedAt` | Date | 记录最后更新时间 |
| `__v` | Number | Mongoose版本控制字段 |

### 虚拟字段

| 字段名 | 类型 | 说明 | 计算逻辑 |
|--------|------|------|----------|
| `id` | String | 字符串格式的ID | `_id.toString()` |
| `stationId` | Object | 当前所在电站信息 | 填充`currentStationId`引用的电站数据 |
| `dispatchStatus` | String | 调度状态 | 比较`homeStationId`和`currentStationId`，相同为`stationed`，不同为`dispatched` |
| `dispatchType` | String | 调度类型 | 当前为`null`，可扩展为`temporary`、`permanent`等 |

### 员工类型说明

| 值 | 说明 |
|----|------|
| `full_time` | 全职员工 |
| `part_time` | 兼职员工 |
| `intern` | 实习生 |
| `external` | 外聘人员 |
| `borrowed` | 借调人员 |

### 员工状态说明

| 值 | 说明 |
|----|------|
| `active` | 在职 |
| `learning` | 学习中 |
| `inactive` | 离职 |
| `vacant` | 空缺 |

### 部门说明

| 值 | 说明 |
|----|------|
| `management` | 管理部门 |
| `operations` | 运营部门 |
| `maintenance` | 维护部门 |
| `security` | 安全部门 |
| `technical` | 技术部门 |

## 2. 电站表 (powerstations)

### 基础字段

| 字段名 | 类型 | 必填 | 业务含义 | 说明 | 示例 |
|--------|------|------|----------|------|------|
| `_id` | ObjectId | 是 | 电站唯一标识 | MongoDB自动生成的唯一标识符，用于数据库内部关联 | `6879de4236b67dd911c11d0b` |
| `name` | String | 是 | 电站名称 | 电站的正式名称，用于显示和识别 | `青海中控10MW` |
| `location` | Object | 是 | 地理位置信息 | 电站的详细地理位置，包含省市地址和坐标 | 见下方location结构 |
| `capacity` | Number | 是 | 装机容量 | 电站的发电装机容量，单位为兆瓦(MW) | `10` |
| `type` | String | 是 | 电站类型 | 电站的发电技术类型，决定运营管理方式 | `solar`、`wind`、`hydro`、`thermal` |
| `status` | String | 是 | 运行状态 | 电站当前的运行状态，影响人员调度和运营计划 | `active`、`maintenance`、`offline` |
| `commissionDate` | Date | 否 | 投运日期 | 电站正式投入商业运行的日期 | `2013-07-15T00:00:00.000Z` |
| `description` | String | 否 | 电站描述 | 电站的详细描述或特殊说明 | `大型光伏发电站` |

### location字段结构

| 字段名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| `coordinates` | Object | 是 | 坐标信息 | 见下方coordinates结构 |
| `province` | String | 是 | 省份 | `青海省` |
| `city` | String | 是 | 城市 | `德令哈市` |
| `address` | String | 是 | 详细地址 | `德令哈市柯柯镇` |

### coordinates字段结构

| 字段名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| `latitude` | Number | 是 | 纬度 | `37.3681` |
| `longitude` | Number | 是 | 经度 | `97.3532` |

### 系统字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `createdAt` | Date | 记录创建时间 |
| `updatedAt` | Date | 记录最后更新时间 |
| `__v` | Number | Mongoose版本控制字段 |

### 虚拟字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `id` | String | 字符串格式的ID |

## 3. 用户表 (users)

### 基础字段

| 字段名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| `_id` | ObjectId | 是 | MongoDB自动生成的唯一标识符 | `6879de4336b67dd911c11dac` |
| `username` | String | 是 | 用户名，唯一 | `admin` |
| `email` | String | 是 | 邮箱地址，唯一 | `<EMAIL>` |
| `password` | String | 是 | 加密后的密码 | `$2b$10$...` |
| `role` | String | 是 | 用户角色 | `admin`、`operator`、`viewer` |
| `isActive` | Boolean | 是 | 账户是否激活 | `true`、`false` |
| `lastLogin` | Date | 否 | 最后登录时间 | `2025-01-18T05:40:18.905Z` |

### 系统字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `createdAt` | Date | 记录创建时间 |
| `updatedAt` | Date | 记录最后更新时间 |
| `__v` | Number | Mongoose版本控制字段 |

### 用户角色说明

| 值 | 说明 | 权限 |
|----|------|------|
| `admin` | 管理员 | 所有权限 |
| `operator` | 操作员 | 查看、编辑员工和调度信息 |
| `viewer` | 查看者 | 仅查看权限 |

## 4. 发电量表 (powergeneration)

### 基础字段

| 字段名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| `_id` | ObjectId | 是 | MongoDB自动生成的唯一标识符 | `6879de4336b67dd911c11db0` |
| `stationId` | ObjectId | 是 | 电站ID，引用PowerStation表 | `6879de4236b67dd911c11d0b` |
| `date` | Date | 是 | 发电日期 | `2025-06-18T16:00:00.000Z` |
| `dailyGeneration` | Number | 是 | 日发电量(MWh) | `420.83` |
| `monthlyGeneration` | Number | 是 | 月发电量(MWh) | `12624.90` |
| `yearlyGeneration` | Number | 是 | 年发电量(MWh) | `153602.99` |
| `totalGeneration` | Number | 是 | 累计发电量(MWh) | `460808.96` |
| `efficiency` | Number | 否 | 发电效率(%) | `85.16` |
| `weather` | String | 否 | 天气状况 | `sunny`、`cloudy`、`rainy` |
| `notes` | String | 否 | 备注信息 | `设备运行正常` |

### 系统字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `createdAt` | Date | 记录创建时间 |
| `updatedAt` | Date | 记录最后更新时间 |
| `__v` | Number | Mongoose版本控制字段 |

## 5. 调度记录表 (dispatchrecords)

### 基础字段

| 字段名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| `_id` | ObjectId | 是 | MongoDB自动生成的唯一标识符 | `6879de4336b67dd911c11db5` |
| `employeeId` | ObjectId | 是 | 员工ID，引用Employee表 | `6879de4236b67dd911c11d15` |
| `fromStationId` | ObjectId | 是 | 调出电站ID，引用PowerStation表 | `6879de4236b67dd911c11d0b` |
| `toStationId` | ObjectId | 是 | 调入电站ID，引用PowerStation表 | `6879de4236b67dd911c11d0c` |
| `dispatchType` | String | 是 | 调度类型 | `temporary`、`permanent`、`emergency` |
| `startDate` | Date | 是 | 调度开始日期 | `2025-01-18T00:00:00.000Z` |
| `endDate` | Date | 否 | 调度结束日期 | `2025-01-25T00:00:00.000Z` |
| `reason` | String | 是 | 调度原因 | `设备维护需要技术支持` |
| `status` | String | 是 | 调度状态 | `pending`、`approved`、`active`、`completed`、`cancelled` |
| `approvedBy` | ObjectId | 否 | 审批人ID，引用User表 | `6879de4336b67dd911c11dac` |
| `approvedAt` | Date | 否 | 审批时间 | `2025-01-18T08:00:00.000Z` |
| `notes` | String | 否 | 备注信息 | `紧急调度，需要立即执行` |

### 系统字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `createdAt` | Date | 记录创建时间 |
| `updatedAt` | Date | 记录最后更新时间 |
| `__v` | Number | Mongoose版本控制字段 |

### 调度类型说明

| 值 | 说明 |
|----|------|
| `temporary` | 临时调度 |
| `permanent` | 永久调度 |
| `emergency` | 紧急调度 |

### 调度状态说明

| 值 | 说明 |
|----|------|
| `pending` | 待审批 |
| `approved` | 已审批 |
| `active` | 执行中 |
| `completed` | 已完成 |
| `cancelled` | 已取消 |

## 索引说明

### 员工表索引
- `username`: 唯一索引
- `email`: 唯一索引
- `homeStationId`: 普通索引
- `currentStationId`: 普通索引
- `status`: 普通索引

### 电站表索引
- `name`: 唯一索引
- `type`: 普通索引
- `status`: 普通索引

### 用户表索引
- `username`: 唯一索引
- `email`: 唯一索引

### 发电量表索引
- `stationId + date`: 复合索引
- `date`: 普通索引

### 调度记录表索引
- `employeeId`: 普通索引
- `fromStationId`: 普通索引
- `toStationId`: 普通索引
- `status`: 普通索引
- `startDate`: 普通索引

## 数据关系说明

1. **员工 ↔ 电站**: 多对一关系
   - 每个员工属于一个所属电站(`homeStationId`)
   - 每个员工当前在一个电站工作(`currentStationId`)

2. **用户 ↔ 员工**: 一对一关系(可选)
   - 用户可以关联到员工记录

3. **发电量 ↔ 电站**: 多对一关系
   - 每条发电量记录属于一个电站

4. **调度记录 ↔ 员工**: 多对一关系
   - 每条调度记录关联一个员工

5. **调度记录 ↔ 电站**: 多对一关系
   - 每条调度记录有调出和调入电站

6. **调度记录 ↔ 用户**: 多对一关系
   - 每条调度记录可能有一个审批人

## API端点总结

### 认证相关
- `POST /api/v1/auth/login` - 用户登录
- `POST /api/v1/auth/register` - 用户注册
- `GET /api/v1/auth/me` - 获取当前用户信息
- `PUT /api/v1/auth/profile` - 更新用户资料
- `PUT /api/v1/auth/password` - 修改密码

### 电站管理
- `GET /api/v1/stations` - 获取电站列表
- `GET /api/v1/stations/:id` - 获取电站详情
- `POST /api/v1/stations` - 创建电站
- `PUT /api/v1/stations/:id` - 更新电站
- `DELETE /api/v1/stations/:id` - 删除电站
- `GET /api/v1/stations/stats` - 获取电站统计数据
- `GET /api/v1/stations/:id/generation-history` - 获取电站发电量历史

### 员工管理
- `GET /api/v1/employees` - 获取员工列表
- `GET /api/v1/employees/:id` - 获取员工详情
- `POST /api/v1/employees` - 创建员工
- `PUT /api/v1/employees/:id` - 更新员工
- `DELETE /api/v1/employees/:id` - 删除员工

### 系统相关
- `GET /api/v1/health` - 健康检查
- `GET /api/v1/websocket/realtime` - 实时数据WebSocket

## 注意事项

1. 所有日期字段都使用UTC时间存储
2. 密码字段使用bcrypt加密存储
3. ObjectId字段在API返回时会自动转换为字符串
4. 虚拟字段不会存储在数据库中，而是在查询时动态计算
5. 删除操作建议使用软删除，通过状态字段标记
6. 重要操作建议记录操作日志