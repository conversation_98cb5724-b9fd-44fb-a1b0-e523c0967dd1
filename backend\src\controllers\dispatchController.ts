import { Request, Response } from 'express';
import mongoose from 'mongoose';
import DispatchRecord, { DispatchStatus, DispatchSource } from '../models/DispatchRecord';
import { Employee } from '../models/Employee';
import { PowerStation } from '../models/PowerStation';
import { generateDispatchQuery, analyzeDispatchRisk, getAIRecommendations } from '../services/enhancedAiDispatchService';
import * as enhancedAiDispatchService from '../services/enhancedAiDispatchService';

// 获取调度记录列表
export const getDispatchRecords = async (req: Request, res: Response) => {
  try {
    const { 
      status, 
      fromStationId, 
      toStationId, 
      startDate, 
      endDate,
      page = 1, 
      limit = 20,
      source 
    } = req.query;

    const filter: any = {};
    
    if (status) filter.status = status;
    if (fromStationId) filter.fromStationId = fromStationId;
    if (toStationId) filter.toStationId = toStationId;
    if (source) filter.source = source;
    
    if (startDate || endDate) {
      filter.startDate = {};
      if (startDate) filter.startDate.$gte = new Date(startDate as string);
      if (endDate) filter.startDate.$lte = new Date(endDate as string);
    }

    const skip = (Number(page) - 1) * Number(limit);
    
    const [records, total] = await Promise.all([
      DispatchRecord.find(filter)
        .populate('employee', 'name employeeType status homeStationId currentStationId')
      .populate('fromStation', 'name')
      .populate('toStation', 'name')
        .populate('reviewer', 'username')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(Number(limit)),
      DispatchRecord.countDocuments(filter)
    ]);

    res.json({
      success: true,
      data: {
        records,
        pagination: {
          current: Number(page),
          pageSize: Number(limit),
          total,
          pages: Math.ceil(total / Number(limit))
        }
      }
    });
  } catch (error) {
    console.error('获取调度记录失败:', error);
    res.status(500).json({
      success: false,
      message: '获取调度记录失败',
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
};

// 创建调度申请
export const createDispatchRecord = async (req: Request, res: Response): Promise<void> => {
  try {
    const {
      employeeId,
      fromStationId,
      toStationId,
      dispatchType,
      startDate,
      endDate,
      reason,
      source = DispatchSource.MANUAL,
      aiQuery,
      batchId,
      isBatch = false
    } = req.body;

    // 验证员工和电站是否存在
    const [employee, fromStation, toStation] = await Promise.all([
      Employee.findById(employeeId),
      PowerStation.findById(fromStationId),
      PowerStation.findById(toStationId)
    ]);

    if (!employee) {
      res.status(404).json({
        success: false,
        message: '员工不存在'
      });
      return;
    }

    if (!fromStation || !toStation) {
      res.status(404).json({
        success: false,
        message: '电站不存在'
      });
      return;
    }

    // 进行风险分析
    const riskAnalysis = await analyzeDispatchRisk({
      employeeId,
      fromStationId,
      toStationId,
      startDate: new Date(startDate),
      endDate: endDate ? new Date(endDate) : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 默认30天
      reason
    });

    const dispatchRecord = new DispatchRecord({
      employeeId,
      fromStationId,
      toStationId,
      dispatchType,
      startDate: new Date(startDate),
      endDate: endDate ? new Date(endDate) : undefined,
      reason,
      source,
      aiQuery,
      batchId,
      isBatch,
      riskAnalysis,
      status: DispatchStatus.PENDING
    });

    await dispatchRecord.save();
    
    // 填充关联数据
    await dispatchRecord.populate([
      { path: 'employee', select: 'name employeeType status homeStationId currentStationId' },
      { path: 'fromStation', select: 'name' },
      { path: 'toStation', select: 'name' }
    ]);

    res.status(201).json({
      success: true,
      data: dispatchRecord,
      message: '调度申请创建成功'
    });
  } catch (error) {
    console.error('创建调度申请失败:', error);
    res.status(500).json({
      success: false,
      message: '创建调度申请失败',
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
};

// 批量创建调度申请
export const createBatchDispatchRecords = async (req: Request, res: Response): Promise<void> => {
  try {
    const { records, reason, source = DispatchSource.MANUAL, aiQuery } = req.body;
    
    if (!Array.isArray(records) || records.length === 0) {
      res.status(400).json({
        success: false,
        message: '调度记录数组不能为空'
      });
      return;
    }

    const batchId = new mongoose.Types.ObjectId().toString();
    const createdRecords = [];

    for (const record of records) {
      const {
        employeeId,
        fromStationId,
        toStationId,
        dispatchType,
        startDate,
        endDate
      } = record;

      // 验证员工和电站
      const [employee, fromStation, toStation] = await Promise.all([
        Employee.findById(employeeId),
        PowerStation.findById(fromStationId),
        PowerStation.findById(toStationId)
      ]);

      if (!employee || !fromStation || !toStation) {
        continue; // 跳过无效记录
      }

      // 进行风险分析
      const riskAnalysis = await analyzeDispatchRisk({
        employeeId,
        fromStationId,
        toStationId,
        startDate: new Date(startDate),
        endDate: endDate ? new Date(endDate) : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 默认30天
        reason
      });

      const dispatchRecord = new DispatchRecord({
        employeeId,
        fromStationId,
        toStationId,
        dispatchType,
        startDate: new Date(startDate),
        endDate: endDate ? new Date(endDate) : undefined,
        reason,
        source,
        aiQuery,
        batchId,
        isBatch: true,
        riskAnalysis,
        status: DispatchStatus.PENDING
      });

      await dispatchRecord.save();
      createdRecords.push(dispatchRecord);
    }

    res.status(201).json({
      success: true,
      data: {
        batchId,
        count: createdRecords.length,
        records: createdRecords
      },
      message: `批量创建${createdRecords.length}条调度申请成功`
    });
  } catch (error) {
    console.error('批量创建调度申请失败:', error);
    res.status(500).json({
      success: false,
      message: '批量创建调度申请失败',
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
};

// AI查询员工
export const aiQueryEmployees = async (req: Request, res: Response): Promise<void> => {
  try {
    const { query } = req.body;
    
    if (!query || typeof query !== 'string') {
      res.status(400).json({
        success: false,
        message: '查询条件不能为空'
      });
      return;
    }

    // 使用百炼大模型生成MongoDB查询
    const queryResult = await generateDispatchQuery(query);

    console.log('🔍 AI生成的查询条件:', queryResult);

    let mongoQuery: any = { status: 'active' };
    let explanation = '基础查询：查找所有活跃员工';
    let confidence = 0.8;

    // generateDispatchQuery 返回的是字符串形式的查询代码
    // 这里我们使用基础查询，因为动态执行代码存在安全风险
    if (typeof queryResult === 'string' && queryResult.includes('电工')) {
      mongoQuery = { 
        status: 'active',
        $or: [
          { skills: { $in: ['电工', '电气维护', '电气运维'] } },
          { position: { $regex: '电工', $options: 'i' } }
        ]
      };
      explanation = 'AI查询解析：查找具有电工技能的活跃员工';
      confidence = 0.9;
    } else if (typeof queryResult === 'string' && queryResult.includes('安全')) {
      mongoQuery = { 
        status: 'active',
        $or: [
          { skills: { $in: ['安全管理', '安全监督'] } },
          { position: { $regex: '安全', $options: 'i' } },
          { certifications: { $in: ['安全员证'] } }
        ]
      };
      explanation = 'AI查询解析：查找具有安全技能的活跃员工';
      confidence = 0.9;
    }

    // 执行MongoDB查询
    const employees = await Employee.find(mongoQuery)
      .populate('homeStationId', 'name')
      .populate('currentStationId', 'name')
      .limit(50); // 限制结果数量

    // 生成风险分析
    const riskAnalysis = `AI查询解析：${explanation}\n置信度：${(confidence * 100).toFixed(1)}%\n查询到 ${employees.length} 名符合条件的员工`;

    res.json({
      success: true,
      query,
      mongoQuery,
      explanation,
      confidence,
      employees,
      count: employees.length,
      riskAnalysis
    });
  } catch (error) {
    console.error('AI查询员工失败:', error);
    res.status(500).json({
      success: false,
      message: 'AI查询员工失败',
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
};

// AI智能推荐
export const getAIDispatchRecommendations = async (req: Request, res: Response): Promise<void> => {
  try {
    const { requirement } = req.body;
    
    if (!requirement || typeof requirement !== 'string') {
      res.status(400).json({
        success: false,
        message: '需求描述不能为空'
      });
      return;
    }

    // 获取AI推荐
    const employees = await Employee.find({ status: 'active' }).limit(50);
    const stations = await PowerStation.find({});
    const aiResult = await getAIRecommendations(requirement, employees, stations);

    // 格式化推荐结果
    const formattedResult = {
      // 推荐的员工列表
      recommendedEmployees: aiResult.recommendedEmployees || [],
      
      // 推荐分析
      analysis: {
        requirement,
        analysisText: aiResult.analysis,
        riskAssessment: aiResult.riskAssessment,
        recommendations: aiResult.recommendations
      },
      
      // 推荐理由
      recommendationReason: {
        mainReasons: [
          aiResult.analysis,
          `基于当前 ${employees.length} 名活跃员工的分析`,
          `考虑了 ${stations.length} 个电站的配置情况`
        ],
        riskFactors: aiResult.riskAssessment ? [aiResult.riskAssessment] : [],
        suggestions: aiResult.recommendations ? [aiResult.recommendations] : []
      },
      
      // 调度建议
      dispatchSuggestion: {
        totalCandidates: (aiResult.recommendedEmployees || []).length,
        recommendedAction: (aiResult.recommendedEmployees || []).length > 0 ? 
          '找到合适的候选员工，建议进一步评估' : 
          '未找到完全匹配的员工，建议调整需求或扩大搜索范围',
        nextSteps: [
          '审查推荐的员工资质',
          '评估调度对源电站的影响',
          '制定详细的调度计划',
          '获得相关部门审批'
        ]
      }
    };

    res.json({
      success: true,
      requirement,
      data: formattedResult,
      // 保持向后兼容
      recommendations: aiResult.recommendations,
      recommendedEmployees: aiResult.recommendedEmployees || [],
      analysis: aiResult.analysis,
      riskAssessment: aiResult.riskAssessment,
      riskAnalysis: `${aiResult.analysis}\n\n风险评估：${aiResult.riskAssessment}`,
      message: `AI推荐完成，找到 ${(aiResult.recommendedEmployees || []).length} 名候选员工`
    });
  } catch (error) {
    console.error('获取AI推荐失败:', error);
    res.status(500).json({
      success: false,
      message: '获取AI推荐失败',
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
};

// 审核调度申请
export const reviewDispatchRecord = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const { action, comments } = req.body; // action: 'approve' | 'reject'
    const reviewerId = (req as any).user?.id; // 假设从认证中间件获取

    const dispatchRecord = await DispatchRecord.findById(id);
    
    if (!dispatchRecord) {
      res.status(404).json({
        success: false,
        message: '调度记录不存在'
      });
      return;
    }

    if (dispatchRecord.status !== DispatchStatus.PENDING) {
      res.status(400).json({
        success: false,
        message: '只能审核待审批的调度申请'
      });
      return;
    }

    const newStatus = action === 'approve' ? DispatchStatus.APPROVED : DispatchStatus.REJECTED;
    
    dispatchRecord.status = newStatus;
    dispatchRecord.reviewerId = reviewerId;
    dispatchRecord.reviewDate = new Date();
    dispatchRecord.reviewComments = comments;

    // 如果审批通过，更新员工的当前电站
    if (action === 'approve') {
      await Employee.findByIdAndUpdate(dispatchRecord.employeeId, {
        currentStationId: dispatchRecord.toStationId,
        status: 'dispatched'
      });
      
      // 将状态更新为执行中
      dispatchRecord.status = DispatchStatus.ACTIVE;
    }

    await dispatchRecord.save();

    await dispatchRecord.populate([
      { path: 'employee', select: 'name employeeType status homeStationId currentStationId' },
      { path: 'fromStation', select: 'name' },
      { path: 'toStation', select: 'name' },
      { path: 'reviewer', select: 'username' }
    ]);

    res.json({
      success: true,
      data: dispatchRecord,
      message: action === 'approve' ? '调度申请审批通过' : '调度申请已拒绝'
    });
  } catch (error) {
    console.error('审核调度申请失败:', error);
    res.status(500).json({
      success: false,
      message: '审核调度申请失败',
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
};

// AI查询接口
export const handleAiQuery = async (req: Request, res: Response): Promise<void> => {
  try {
    const { query } = req.body;
    
    if (!query) {
      res.status(400).json({ error: '查询内容不能为空' });
      return;
    }

    const result = await generateDispatchQuery(query);
    res.json(result);
  } catch (error) {
    console.error('AI查询失败:', error);
    res.status(500).json({ error: 'AI查询失败' });
  }
};

// AI推荐接口（原版）
export const handleAiRecommendation = async (req: Request, res: Response): Promise<void> => {
  try {
    const { requirement, targetStationId, urgencyLevel } = req.body;
    
    if (!requirement) {
      res.status(400).json({ error: '需求描述不能为空' });
      return;
    }

    const employees = await Employee.find({ status: 'active' }).limit(50);
    const stations = await PowerStation.find({});
    const result = await getAIRecommendations(requirement, employees, stations);
    res.json(result);
  } catch (error) {
    console.error('AI推荐失败:', error);
    res.status(500).json({ error: 'AI推荐失败' });
  }
};

// 增强版AI推荐接口
export const handleEnhancedAiRecommendation = async (req: Request, res: Response): Promise<void> => {
  try {
    const { 
      query, 
      requiredSkills, 
      preferredExperience, 
      duration, 
      startDate, 
      config, 
      urgencyLevel = 'medium',
      targetStationId,
      maxCandidates = 10
    } = req.body;
    
    if (!query) {
      res.status(400).json({ 
        success: false,
        message: '查询内容不能为空' 
      });
      return;
    }

    const request = {
      requirement: query,
      targetStationId,
      requiredSkills: requiredSkills || [],
      preferredExperience: preferredExperience || 0,
      urgencyLevel,
      duration: duration || 30,
      startDate: startDate ? new Date(startDate) : new Date(),
      maxCandidates,
      config: config || undefined
    };

    const result = await enhancedAiDispatchService.getEnhancedAIRecommendations(request);
    
    // 格式化推荐结果，提供更详细的信息
    const formattedRecommendations = result.recommendations.map(rec => ({
      // 员工基本信息
      employee: {
        id: rec.employee._id,
        name: rec.employee.name,
        employeeType: rec.employee.employeeType,
        status: rec.employee.status,
        skills: rec.employee.skills || [],
        certifications: rec.employee.certifications || [],
        currentStation: rec.employee.currentStation,
        homeStation: rec.employee.homeStation
      },
      
      // 推荐评分详情
      scores: {
        overall: rec.overallScore,
        skillMatch: rec.skillMatchScore,
        experience: rec.experienceScore,
        availability: rec.availabilityScore,
        risk: rec.riskScore
      },
      
      // 技能匹配分析
      skillAnalysis: {
        matchedSkills: rec.matchedSkills,
        missingSkills: rec.missingSkills,
        skillMatchPercentage: Math.round((rec.matchedSkills.length / (rec.matchedSkills.length + rec.missingSkills.length)) * 100) || 0
      },
      
      // 推荐理由
      recommendation: {
        strengths: rec.strengthReasons,
        risks: rec.riskFactors,
        alternativeOptions: rec.alternativeOptions,
        learningOpportunities: rec.learningOpportunities,
        skillDevelopment: rec.skillDevelopment
      },
      
      // 调度计划建议
      dispatchPlan: {
        recommendedDuration: rec.recommendedDuration,
        suggestedStartDate: rec.suggestedStartDate,
        estimatedEndDate: new Date(rec.suggestedStartDate.getTime() + rec.recommendedDuration * 24 * 60 * 60 * 1000)
      },
      
      // 员工状态
      status: {
        isAvailable: rec.businessStatus.isAvailable,
        fatigueLevel: rec.businessStatus.fatigueScore,
        recentDispatches: rec.businessStatus.recentDispatches,
        continuousDispatches: rec.businessStatus.continuousDispatches,
        performanceRating: rec.businessStatus.performanceRating
      }
    }));

    // 生成推荐总结
    const recommendationSummary = {
      totalCandidates: result.summary.totalCandidates,
      averageScore: result.summary.averageScore,
      riskLevel: result.summary.riskLevel,
      confidence: result.summary.confidence,
      bestCandidate: formattedRecommendations.length > 0 ? formattedRecommendations[0].employee.name : null,
      recommendationReason: result.analysis
    };

    res.json({
      success: true,
      data: {
        // 推荐的员工列表（按评分排序）
        recommendedEmployees: formattedRecommendations,
        
        // 推荐总结
        summary: recommendationSummary,
        
        // 详细分析报告
        analysis: {
          requirement: query,
          analysisText: result.analysis,
          riskAssessment: result.riskAssessment,
          alternatives: result.alternatives,
          configurationUsed: result.configUsed
        },
        
        // 调度建议
        dispatchRecommendation: {
          urgencyLevel,
          recommendedAction: result.summary.riskLevel === 'low' ? '建议执行调度' : 
                           result.summary.riskLevel === 'medium' ? '谨慎执行，注意风险控制' :
                           result.summary.riskLevel === 'high' ? '建议重新评估或寻找替代方案' :
                           '不建议执行，风险过高',
          keyConsiderations: [
            `共找到 ${result.summary.totalCandidates} 名候选员工`,
            `平均匹配度: ${result.summary.averageScore}%`,
            `整体风险等级: ${result.summary.riskLevel}`,
            `推荐置信度: ${Math.round(result.summary.confidence * 100)}%`
          ]
        }
      },
      message: `AI智能推荐完成，找到 ${result.summary.totalCandidates} 名候选员工`
    });
  } catch (error) {
    console.error('增强版AI推荐失败:', error);
    res.status(500).json({
      success: false,
      message: '增强版AI推荐失败',
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
};

// 获取推荐配置
export const getRecommendationConfig = async (req: Request, res: Response): Promise<void> => {
  try {
    // 这里应该从数据库获取配置，暂时返回默认配置
    const defaultConfig = {
      skillMatchWeight: 30,
      experienceWeight: 25,
      positionMatchWeight: 20,
      locationWeight: 15,
      maxContinuousDispatches: 3,
      minRestDaysBetweenDispatches: 2,
      fatigueThreshold: 70,
      minStaffRemaining: 2,
      criticalPositionProtection: ['安全员', '技术主管'],
      emergencyResponseTime: 4,
      preferInternalTransfer: true,
      preferExperiencedStaff: true,
      considerTrainingNeeds: false,
      allowOvertimeDispatch: false,
      allowCrossRegionDispatch: true,
      requireManagerApproval: true
    };
    
    res.json({
      success: true,
      data: defaultConfig
    });
  } catch (error) {
    console.error('Get recommendation config error:', error);
    res.status(500).json({
      success: false,
      message: '获取推荐配置失败',
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
};

// 更新推荐配置
export const updateRecommendationConfig = async (req: Request, res: Response): Promise<void> => {
  try {
    const config = req.body;
    
    // 这里应该保存到数据库，暂时只返回成功
    console.log('Updating recommendation config:', config);
    
    res.json({
      success: true,
      message: '推荐配置更新成功',
      data: config
    });
  } catch (error) {
    console.error('Update recommendation config error:', error);
    res.status(500).json({
      success: false,
      message: '更新推荐配置失败',
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
};

// 获取调度统计
export const getDispatchStatistics = async (req: Request, res: Response): Promise<void> => {
  try {
    const { startDate, endDate } = req.query;
    
    const dateFilter: any = {};
    if (startDate || endDate) {
      dateFilter.createdAt = {};
      if (startDate) dateFilter.createdAt.$gte = new Date(startDate as string);
      if (endDate) dateFilter.createdAt.$lte = new Date(endDate as string);
    }

    const [
      totalRecords,
      pendingRecords,
      approvedRecords,
      activeRecords,
      completedRecords,
      rejectedRecords,
      bySource,
      byRiskLevel
    ] = await Promise.all([
      DispatchRecord.countDocuments(dateFilter),
      DispatchRecord.countDocuments({ ...dateFilter, status: DispatchStatus.PENDING }),
      DispatchRecord.countDocuments({ ...dateFilter, status: DispatchStatus.APPROVED }),
      DispatchRecord.countDocuments({ ...dateFilter, status: DispatchStatus.ACTIVE }),
      DispatchRecord.countDocuments({ ...dateFilter, status: DispatchStatus.COMPLETED }),
      DispatchRecord.countDocuments({ ...dateFilter, status: DispatchStatus.REJECTED }),
      DispatchRecord.aggregate([
        { $match: dateFilter },
        { $group: { _id: '$source', count: { $sum: 1 } } }
      ]),
      DispatchRecord.aggregate([
        { $match: dateFilter },
        { $group: { _id: '$riskAnalysis.level', count: { $sum: 1 } } }
      ])
    ]);

    res.json({
      success: true,
      data: {
        total: totalRecords,
        byStatus: {
          pending: pendingRecords,
          approved: approvedRecords,
          active: activeRecords,
          completed: completedRecords,
          rejected: rejectedRecords
        },
        bySource: bySource.reduce((acc: any, item: any) => {
          acc[item._id] = item.count;
          return acc;
        }, {}),
        byRiskLevel: byRiskLevel.reduce((acc: any, item: any) => {
          if (item._id) acc[item._id] = item.count;
          return acc;
        }, {})
      }
    });
  } catch (error) {
    console.error('获取调度统计失败:', error);
    res.status(500).json({
      success: false,
      message: '获取调度统计失败',
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
};