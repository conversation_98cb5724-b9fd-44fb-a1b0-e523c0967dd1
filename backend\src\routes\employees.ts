import express from 'express';
import {
  getAllEmployees,
  getEmployeeById,
  createEmployee,
  updateEmployee,
  deleteEmployee,
  getEmployeesByStation,
  getCurrentEmployeesByStation,
  getHomeEmployeesByStation,
  getDispatchedInEmployees,
  getDispatchedOutEmployees,
  getStationDispatchOverview,
  getEmployeePositions,
  getEmployeeWorkHistory,
  getEmployeesByType,
  getEmployeeStats
} from '../controllers/employeeController';
import { authenticateToken, authorizeRoles } from '../middleware/auth';

const router = express.Router();

// 所有员工路由都需要认证
router.use(authenticateToken);

// 获取所有员工
router.get('/', getAllEmployees);

// 根据ID获取员工
router.get('/:id', getEmployeeById);

// 根据电站ID获取员工（向后兼容）
router.get('/station/:stationId', getEmployeesByStation);

// 根据电站ID获取当前在该电站工作的员工
router.get('/station/:stationId/current', getCurrentEmployeesByStation);

// 根据电站ID获取所属该电站的员工
router.get('/station/:stationId/home', getHomeEmployeesByStation);

// 获取调入该电站的员工
router.get('/station/:stationId/dispatched-in', getDispatchedInEmployees);

// 获取从该电站调出的员工
router.get('/station/:stationId/dispatched-out', getDispatchedOutEmployees);

// 获取电站的员工调度概览
router.get('/station/:stationId/overview', getStationDispatchOverview);

// 获取员工统计信息
router.get('/stats', getEmployeeStats);

// 根据员工类型获取员工
router.get('/type/:type', getEmployeesByType);

// 获取员工的岗位信息
router.get('/:employeeId/positions', getEmployeePositions);

// 获取员工的工作历史
router.get('/:employeeId/work-history', getEmployeeWorkHistory);

// 创建员工 (需要管理员权限)
router.post('/', authorizeRoles('admin', 'manager'), createEmployee);

// 更新员工 (需要管理员权限)
router.put('/:id', authorizeRoles('admin', 'manager'), updateEmployee);

// 删除员工 (需要管理员权限)
router.delete('/:id', authorizeRoles('admin'), deleteEmployee);

export default router;