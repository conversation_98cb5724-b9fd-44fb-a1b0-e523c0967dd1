import React from 'react';
import { Icons } from '../../constants/index';

interface RiskAnalysisModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const RiskAnalysisModal: React.FC<RiskAnalysisModalProps> = ({ isOpen, onClose }) => {
  if (!isOpen) return null;

  const riskFactors = [
    { name: '人员技能匹配度', level: 'low', score: 85, description: '员工技能与岗位要求匹配度较高' },
    { name: '工作负荷平衡', level: 'medium', score: 72, description: '部分电站工作负荷偏高' },
    { name: '人员稳定性', level: 'low', score: 90, description: '员工流动性较低，稳定性好' },
    { name: '应急响应能力', level: 'medium', score: 68, description: '应急人员储备需要加强' }
  ];

  const getRiskColor = (level: string) => {
    switch (level) {
      case 'low': return 'text-green-600 bg-green-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'high': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getRiskText = (level: string) => {
    switch (level) {
      case 'low': return '低风险';
      case 'medium': return '中风险';
      case 'high': return '高风险';
      default: return level;
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-4xl max-h-[80vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-xl font-semibold">风险分析报告</h3>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <Icons.X className="w-6 h-6" />
          </button>
        </div>

        {/* 总体风险评估 */}
        <div className="mb-6 p-4 bg-green-50 rounded-lg border border-green-200">
          <div className="flex items-center mb-2">
            <Icons.Shield className="w-6 h-6 text-green-600 mr-2" />
            <h4 className="text-lg font-medium text-green-800">总体风险评估</h4>
          </div>
          <div className="text-2xl font-bold text-green-600 mb-2">低风险</div>
          <p className="text-green-700">
            当前调度方案整体风险较低，建议继续执行。需要关注工作负荷平衡和应急响应能力。
          </p>
        </div>

        {/* 风险因子详情 */}
        <div className="mb-6">
          <h4 className="text-lg font-medium mb-4">风险因子分析</h4>
          <div className="space-y-4">
            {riskFactors.map((factor, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-4">
                <div className="flex justify-between items-start mb-2">
                  <h5 className="font-medium text-gray-900">{factor.name}</h5>
                  <span className={`px-2 py-1 text-xs font-semibold rounded-full ${getRiskColor(factor.level)}`}>
                    {getRiskText(factor.level)}
                  </span>
                </div>
                
                <div className="mb-2">
                  <div className="flex justify-between text-sm text-gray-600 mb-1">
                    <span>风险评分</span>
                    <span>{factor.score}/100</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full ${
                        factor.score >= 80 ? 'bg-green-500' :
                        factor.score >= 60 ? 'bg-yellow-500' : 'bg-red-500'
                      }`}
                      style={{ width: `${factor.score}%` }}
                    />
                  </div>
                </div>
                
                <p className="text-sm text-gray-600">{factor.description}</p>
              </div>
            ))}
          </div>
        </div>

        {/* 建议措施 */}
        <div className="mb-6">
          <h4 className="text-lg font-medium mb-4">建议措施</h4>
          <div className="space-y-3">
            <div className="flex items-start">
              <Icons.CheckCircle className="w-5 h-5 text-green-600 mr-2 mt-0.5" />
              <span className="text-gray-700">优化高负荷电站的人员配置，适当增加人手</span>
            </div>
            <div className="flex items-start">
              <Icons.CheckCircle className="w-5 h-5 text-green-600 mr-2 mt-0.5" />
              <span className="text-gray-700">建立应急人员储备池，提高应急响应能力</span>
            </div>
            <div className="flex items-start">
              <Icons.CheckCircle className="w-5 h-5 text-green-600 mr-2 mt-0.5" />
              <span className="text-gray-700">定期进行技能培训，提升员工综合能力</span>
            </div>
            <div className="flex items-start">
              <Icons.CheckCircle className="w-5 h-5 text-green-600 mr-2 mt-0.5" />
              <span className="text-gray-700">建立轮岗机制，平衡各电站工作负荷</span>
            </div>
          </div>
        </div>

        {/* 关闭按钮 */}
        <div className="flex justify-end">
          <button
            onClick={onClose}
            className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            关闭
          </button>
        </div>
      </div>
    </div>
  );
};

export default RiskAnalysisModal;