# 数据库字典详细文档

## 数据库概览

### 基本信息
- **数据库名称**: `power_station_management`
- **数据库类型**: MongoDB 5.0+
- **连接地址**: `mongodb://localhost:27017/power_station_management`
- **字符编码**: UTF-8
- **时区**: UTC

### 业务领域
电站人员调度管理系统专注于电力行业的人力资源管理，涵盖以下核心业务：

1. **多电站管理**: 支持集团化电力企业的多个发电站点管理
2. **复杂岗位体系**: 支持主岗位、兼职岗位的多维度岗位管理
3. **动态人员调度**: 支持临时调度、永久调度、紧急调度等多种调度模式
4. **权限分级管理**: 支持管理员、操作员、查看者的分级权限控制
5. **实时数据监控**: 支持发电量、人员状态的实时数据更新

## 数据表详细说明

### 1. 员工表 (employees) - 核心人员信息

#### 表结构概述
员工表是系统的核心表之一，存储所有人员的基本信息、岗位信息和状态信息。支持复杂的人员调度业务逻辑。

#### 字段详细说明

##### 身份标识字段
- **`_id`** (ObjectId): 系统唯一标识符
  - 业务含义: 员工在系统中的唯一身份标识
  - 使用场景: 数据库关联、API查询、前端路由参数
  - 约束条件: MongoDB自动生成，全局唯一

- **`name`** (String): 员工姓名
  - 业务含义: 员工的真实姓名，用于显示和人工识别
  - 使用场景: 界面显示、报表生成、搜索查询
  - 约束条件: 必填，长度2-20字符
  - 数据示例: "张三"、"李四"、"王经理"

##### 组织归属字段
- **`homeStationId`** (ObjectId): 归属电站
  - 业务含义: 员工编制所属的电站，决定基本工作地点和管理归属
  - 使用场景: 组织架构管理、工资发放、绩效考核
  - 关联关系: 外键关联 powerstations._id
  - 业务规则: 一旦设定通常不轻易变更

- **`currentStationId`** (ObjectId): 当前工作电站
  - 业务含义: 员工当前实际工作的电站，可能因调度而临时变更
  - 使用场景: 日常管理、考勤统计、应急联系
  - 关联关系: 外键关联 powerstations._id
  - 业务规则: 可通过调度流程动态变更

##### 职位信息字段
- **`position`** (String): 职位名称
  - 业务含义: 员工的具体职位或岗位名称
  - 使用场景: 权限控制、工作分配、薪资等级
  - 数据示例: "项目经理"、"值长"、"运维工程师"、"安全员"
  - 业务规则: 与岗位模板表关联，但允许灵活设置

- **`department`** (String): 所属部门
  - 业务含义: 员工所属的业务部门或职能部门
  - 使用场景: 组织架构、报告关系、预算分配
  - 枚举值: 
    - `management`: 管理部门
    - `operations`: 运营部门  
    - `maintenance`: 维护部门
    - `safety`: 安全部门
    - `technical`: 技术部门

##### 雇佣关系字段
- **`employeeType`** (String): 员工类型
  - 业务含义: 员工的雇佣关系类型，影响管理方式和权限
  - 使用场景: 权限控制、薪资计算、调度限制
  - 枚举值:
    - `full_time`: 全职员工 - 正式编制员工
    - `part_time`: 兼职员工 - 非全日制员工
    - `intern`: 实习生 - 在校学生实习
    - `external`: 外聘人员 - 外包公司员工
    - `borrowed`: 借调人员 - 从其他单位临时借调

- **`status`** (String): 员工状态
  - 业务含义: 员工当前的工作状态，影响调度和管理
  - 使用场景: 人员调度、统计报表、系统权限
  - 枚举值:
    - `active`: 在职 - 正常工作状态
    - `learning`: 学习中 - 培训或学习状态
    - `inactive`: 离职 - 已离开公司
    - `vacant`: 空缺 - 岗位空缺状态

##### 时间信息字段
- **`hireDate`** (Date): 入职日期
  - 业务含义: 员工正式入职的日期
  - 使用场景: 工龄计算、试用期管理、年假计算
  - 数据格式: ISO 8601 UTC时间
  - 业务规则: 不能晚于当前日期

##### 能力资质字段
- **`skills`** (Array[String]): 技能清单
  - 业务含义: 员工掌握的专业技能和能力
  - 使用场景: 岗位匹配、调度决策、培训规划
  - 数据示例: ["电站管理", "安全监督", "设备维护"]
  - 业务规则: 支持动态添加和删除

- **`certifications`** (Array[String]): 资质证书
  - 业务含义: 员工持有的专业证书和资质
  - 使用场景: 岗位资格验证、合规检查、能力评估
  - 数据示例: ["电力工程师", "安全工程师", "焊工证"]
  - 业务规则: 需要定期更新和验证

##### 联系信息字段
- **`phone`** (String): 联系电话
  - 业务含义: 员工的主要联系电话
  - 使用场景: 紧急联系、通知发送、考勤确认
  - 数据格式: 支持手机号和固话
  - 隐私保护: 敏感信息，需要权限控制

- **`email`** (String): 电子邮箱
  - 业务含义: 员工的邮箱地址
  - 使用场景: 系统通知、文档发送、账号关联
  - 数据格式: 标准邮箱格式验证
  - 业务规则: 可选填，但建议填写

##### 备注信息字段
- **`notes`** (String): 备注信息
  - 业务含义: 员工的额外信息或特殊说明
  - 使用场景: 特殊情况记录、管理备忘、历史信息
  - 数据示例: "经验丰富的项目经理"、"有高空作业证"
  - 业务规则: 自由文本，支持富文本格式

#### 虚拟字段说明

##### 调度状态字段
- **`dispatchStatus`** (Virtual): 调度状态
  - 计算逻辑: 比较 homeStationId 和 currentStationId
  - 返回值: 
    - `stationed`: 在归属电站工作
    - `dispatched`: 被调度到其他电站
  - 业务含义: 快速判断员工是否处于调度状态

- **`dispatchType`** (Virtual): 调度类型
  - 当前状态: 预留字段，暂时返回 null
  - 扩展计划: 可扩展为 temporary、permanent 等
  - 业务含义: 区分不同类型的调度

#### 索引策略
- **主键索引**: _id (MongoDB默认)
- **唯一索引**: 无 (允许同名员工)
- **复合索引**: 
  - homeStationId + status (查询电站员工)
  - currentStationId + status (查询当前在站员工)
  - department + employeeType (部门统计)
- **文本索引**: name (支持姓名搜索)

#### 数据完整性约束
1. homeStationId 必须存在于 powerstations 表中
2. currentStationId 必须存在于 powerstations 表中
3. employeeType 必须为预定义枚举值
4. status 必须为预定义枚举值
5. hireDate 不能晚于当前日期
6. phone 格式验证（如果提供）
7. email 格式验证（如果提供）

### 2. 电站表 (powerstations) - 发电站点信息

#### 表结构概述
电站表存储所有发电站点的基本信息，是系统的基础数据表。每个电站都有独立的人员配置和运营数据。

#### 字段详细说明

##### 基本标识字段
- **`_id`** (ObjectId): 电站唯一标识符
  - 业务含义: 电站在系统中的唯一身份标识
  - 使用场景: 员工归属、调度目标、数据关联
  - 约束条件: MongoDB自动生成，全局唯一

- **`name`** (String): 电站名称
  - 业务含义: 电站的正式名称或简称
  - 使用场景: 界面显示、报表标题、搜索查询
  - 数据示例: "青海中控10MW"、"新疆吐鲁番50MW"
  - 约束条件: 必填，全局唯一

##### 地理位置字段
- **`location`** (Object): 地理位置信息
  - 业务含义: 电站的详细地理位置信息
  - 使用场景: 地图显示、距离计算、区域管理
  - 子字段结构:
    - `province`: 省份/自治区
    - `city`: 城市/地区
    - `address`: 详细地址
    - `coordinates`: 经纬度坐标

##### 技术参数字段
- **`capacity`** (Number): 装机容量
  - 业务含义: 电站的发电装机容量
  - 单位: 兆瓦(MW)
  - 使用场景: 发电量计算、规模分类、投资评估
  - 数据范围: 大于0的数值
  - 业务规则: 影响人员配置标准

- **`type`** (String): 电站类型
  - 业务含义: 电站的发电技术类型
  - 使用场景: 专业技能匹配、运营模式区分
  - 枚举值:
    - `solar`: 太阳能发电站
    - `wind`: 风力发电站
    - `hydro`: 水力发电站
    - `thermal`: 火力发电站
  - 业务规则: 不同类型需要不同的专业技能

##### 运营状态字段
- **`status`** (String): 运行状态
  - 业务含义: 电站当前的运行状态
  - 使用场景: 调度决策、维护计划、统计报表
  - 枚举值:
    - `active`: 正常运行
    - `maintenance`: 维护中
    - `offline`: 停机状态
  - 业务规则: 影响人员调度和工作安排

##### 时间信息字段
- **`commissionDate`** (Date): 投运日期
  - 业务含义: 电站正式投入商业运行的日期
  - 使用场景: 运行年限计算、维护周期规划
  - 数据格式: ISO 8601 UTC时间
  - 业务规则: 不能晚于当前日期

- **`description`** (String): 电站描述
  - 业务含义: 电站的详细描述或特殊说明
  - 使用场景: 详情展示、特殊情况记录
  - 数据示例: "大型光伏发电站"、"山地风电场"
  - 业务规则: 可选填写

#### 地理位置子结构详解

##### coordinates 坐标信息
- **`latitude`** (Number): 纬度
  - 数据范围: -90 到 90
  - 精度要求: 小数点后6位
  - 业务用途: 地图定位、距离计算

- **`longitude`** (Number): 经度
  - 数据范围: -180 到 180
  - 精度要求: 小数点后6位
  - 业务用途: 地图定位、距离计算

#### 索引策略
- **主键索引**: _id (MongoDB默认)
- **唯一索引**: name (电站名称唯一)
- **普通索引**: 
  - type (按类型查询)
  - status (按状态查询)
  - location.province (按省份查询)
- **地理索引**: location.coordinates (地理位置查询)

### 3. 岗位模板表 (positions) - 标准岗位定义

#### 表结构概述
岗位模板表定义了系统中所有标准岗位的基本信息，为电站岗位配置提供模板基础。

#### 字段详细说明

##### 基本信息字段
- **`positionName`** (String): 岗位名称
  - 业务含义: 标准化的岗位名称
  - 使用场景: 岗位配置、权限分配、技能匹配
  - 数据示例: "项目经理"、"值长"、"安全员"
  - 约束条件: 必填，建议唯一

- **`department`** (String): 所属部门
  - 业务含义: 岗位所属的业务部门
  - 使用场景: 组织架构、权限控制
  - 枚举值: 与员工表department字段一致

- **`level`** (String): 岗位级别
  - 业务含义: 岗位的技能等级或管理层级
  - 使用场景: 薪资等级、权限分级、晋升路径
  - 枚举值:
    - `junior`: 初级岗位
    - `senior`: 高级岗位
    - `expert`: 专家级岗位

##### 技能要求字段
- **`skillRequirements`** (Array[String]): 技能要求
  - 业务含义: 该岗位要求的专业技能
  - 使用场景: 人员匹配、培训规划、招聘需求
  - 数据示例: ["电气知识", "安全管理", "设备维护"]

- **`certificationRequirements`** (Array[String]): 证书要求
  - 业务含义: 该岗位要求的资质证书
  - 使用场景: 资格验证、合规检查
  - 数据示例: ["电工证", "安全员证", "特种作业证"]

##### 状态字段
- **`isActive`** (Boolean): 是否启用
  - 业务含义: 岗位模板是否可用于配置
  - 使用场景: 岗位管理、系统维护
  - 默认值: true

### 4. 电站岗位配置表 (stationpositions) - 具体岗位分配

#### 表结构概述
电站岗位配置表记录每个电站的具体岗位设置和人员分配，是主岗位管理的核心表。

#### 字段详细说明

##### 关联关系字段
- **`stationId`** (ObjectId): 电站ID
  - 业务含义: 岗位所属的电站
  - 关联关系: 外键关联 powerstations._id
  - 使用场景: 电站人员管理、岗位统计

- **`positionId`** (ObjectId): 岗位模板ID
  - 业务含义: 引用的标准岗位模板
  - 关联关系: 外键关联 positions._id
  - 使用场景: 岗位标准化、技能匹配

- **`employeeId`** (ObjectId): 员工ID
  - 业务含义: 分配到该岗位的员工
  - 关联关系: 外键关联 employees._id
  - 使用场景: 人员分配、岗位查询
  - 业务规则: 可为空表示岗位空缺

##### 岗位属性字段
- **`status`** (String): 岗位状态
  - 业务含义: 该岗位的当前状态
  - 枚举值:
    - `active`: 正常使用
    - `vacant`: 空缺待补
    - `suspended`: 暂停使用
  - 使用场景: 岗位管理、人员调度

- **`priority`** (Number): 优先级
  - 业务含义: 岗位的重要程度或优先级
  - 数据范围: 1-10，数值越大优先级越高
  - 使用场景: 人员分配、应急调度

##### 时间信息字段
- **`assignedDate`** (Date): 分配日期
  - 业务含义: 员工分配到该岗位的日期
  - 使用场景: 任职时间统计、轮岗管理

- **`effectiveDate`** (Date): 生效日期
  - 业务含义: 岗位配置生效的日期
  - 使用场景: 延期生效、历史追溯

### 5. 兼职岗位表 (concurrentpositions) - 兼职岗位管理

#### 表结构概述
兼职岗位表记录员工的兼职岗位信息，支持一人多岗的复杂业务场景。

#### 字段详细说明

##### 关联关系字段
- **`employeeId`** (ObjectId): 员工ID
  - 业务含义: 兼职的员工
  - 关联关系: 外键关联 employees._id
  - 业务规则: 必须是已存在的员工

- **`stationId`** (ObjectId): 兼职电站ID
  - 业务含义: 兼职工作的电站
  - 关联关系: 外键关联 powerstations._id
  - 业务规则: 可以与员工归属电站不同

- **`positionId`** (ObjectId): 兼职岗位ID
  - 业务含义: 兼职的具体岗位
  - 关联关系: 外键关联 positions._id
  - 业务规则: 应与员工技能匹配

##### 兼职属性字段
- **`workload`** (Number): 工作量占比
  - 业务含义: 兼职工作占总工作时间的比例
  - 数据范围: 0-100，表示百分比
  - 使用场景: 工作量统计、薪资计算

- **`status`** (String): 兼职状态
  - 业务含义: 兼职岗位的当前状态
  - 枚举值:
    - `active`: 正常兼职
    - `suspended`: 暂停兼职
    - `terminated`: 结束兼职

##### 时间管理字段
- **`startDate`** (Date): 开始日期
  - 业务含义: 兼职开始的日期
  - 使用场景: 兼职期限管理

- **`endDate`** (Date): 结束日期
  - 业务含义: 兼职结束的日期
  - 使用场景: 临时兼职管理
  - 业务规则: 可为空表示长期兼职

### 6. 调度记录表 (dispatchrecords) - 人员调度管理

#### 表结构概述
调度记录表记录所有人员调度的申请、审批和执行过程，是调度管理的核心表。

#### 字段详细说明

##### 调度主体字段
- **`employeeId`** (ObjectId): 被调度员工
  - 业务含义: 参与调度的员工
  - 关联关系: 外键关联 employees._id
  - 使用场景: 调度查询、员工历史

- **`fromStationId`** (ObjectId): 调出电站
  - 业务含义: 员工调出的原电站
  - 关联关系: 外键关联 powerstations._id
  - 业务规则: 通常为员工当前所在电站

- **`toStationId`** (ObjectId): 调入电站
  - 业务含义: 员工调入的目标电站
  - 关联关系: 外键关联 powerstations._id
  - 业务规则: 不能与调出电站相同

##### 调度类型字段
- **`dispatchType`** (String): 调度类型
  - 业务含义: 调度的性质和期限
  - 枚举值:
    - `temporary`: 临时调度 - 短期支援
    - `permanent`: 永久调度 - 长期调动
    - `emergency`: 紧急调度 - 应急支援
  - 使用场景: 调度管理、统计分析

- **`reason`** (String): 调度原因
  - 业务含义: 发起调度的具体原因
  - 使用场景: 审批参考、历史查询
  - 数据示例: "设备维护需要技术支持"、"人员短缺"

##### 时间计划字段
- **`startDate`** (Date): 开始日期
  - 业务含义: 调度计划开始的日期
  - 使用场景: 调度执行、时间管理
  - 业务规则: 不能早于申请日期

- **`endDate`** (Date): 结束日期
  - 业务含义: 调度计划结束的日期
  - 使用场景: 临时调度管理
  - 业务规则: 永久调度可为空

##### 审批流程字段
- **`status`** (String): 调度状态
  - 业务含义: 调度申请的当前状态
  - 枚举值:
    - `pending`: 待审批 - 刚提交申请
    - `approved`: 已审批 - 通过审批
    - `active`: 执行中 - 正在调度
    - `completed`: 已完成 - 调度结束
    - `cancelled`: 已取消 - 取消调度
  - 使用场景: 流程控制、状态查询

- **`approvedBy`** (ObjectId): 审批人
  - 业务含义: 审批该调度申请的用户
  - 关联关系: 外键关联 users._id
  - 使用场景: 审批追溯、权限验证

- **`approvedAt`** (Date): 审批时间
  - 业务含义: 审批操作的时间
  - 使用场景: 审批效率统计、时间追溯

##### 备注信息字段
- **`notes`** (String): 备注信息
  - 业务含义: 调度的额外说明或特殊要求
  - 使用场景: 执行指导、特殊情况记录
  - 数据示例: "紧急调度，需要立即执行"

### 7. 用户表 (users) - 系统用户管理

#### 表结构概述
用户表管理系统的登录用户，控制系统访问权限和操作权限。

#### 字段详细说明

##### 身份认证字段
- **`username`** (String): 用户名
  - 业务含义: 用户登录的唯一标识
  - 约束条件: 必填，全局唯一
  - 数据格式: 字母数字组合，3-20字符
  - 使用场景: 登录验证、用户识别

- **`email`** (String): 邮箱地址
  - 业务含义: 用户的邮箱地址
  - 约束条件: 必填，全局唯一，格式验证
  - 使用场景: 密码重置、系统通知
  - 隐私保护: 敏感信息，需要权限控制

- **`password`** (String): 密码
  - 业务含义: 用户登录密码的加密存储
  - 加密方式: bcrypt加密，不可逆
  - 安全要求: 最少8位，包含字母数字
  - 使用场景: 登录验证

##### 权限控制字段
- **`role`** (String): 用户角色
  - 业务含义: 用户在系统中的权限级别
  - 枚举值:
    - `admin`: 管理员 - 所有权限
    - `operator`: 操作员 - 查看编辑权限
    - `viewer`: 查看者 - 仅查看权限
  - 使用场景: 权限控制、功能限制

- **`isActive`** (Boolean): 账户状态
  - 业务含义: 账户是否可以正常使用
  - 默认值: true
  - 使用场景: 账户管理、安全控制

##### 活动记录字段
- **`lastLogin`** (Date): 最后登录时间
  - 业务含义: 用户最近一次登录的时间
  - 使用场景: 活跃度统计、安全监控
  - 更新时机: 每次成功登录后更新

### 8. 发电量表 (powergeneration) - 发电数据管理

#### 表结构概述
发电量表记录各电站的发电量数据，支持日、月、年度的发电量统计和分析。

#### 字段详细说明

##### 关联关系字段
- **`stationId`** (ObjectId): 电站ID
  - 业务含义: 发电数据所属的电站
  - 关联关系: 外键关联 powerstations._id
  - 使用场景: 电站发电统计、对比分析

- **`date`** (Date): 发电日期
  - 业务含义: 发电数据的统计日期
  - 数据格式: 日期格式，通常为当日0点
  - 使用场景: 时间序列分析、趋势统计
  - 索引策略: 与stationId组成复合索引

##### 发电量数据字段
- **`dailyGeneration`** (Number): 日发电量
  - 业务含义: 当日的发电量
  - 单位: 兆瓦时(MWh)
  - 数据范围: 大于等于0
  - 使用场景: 日报表、短期分析

- **`monthlyGeneration`** (Number): 月发电量
  - 业务含义: 当月累计发电量
  - 单位: 兆瓦时(MWh)
  - 计算方式: 月内日发电量累加
  - 使用场景: 月度报表、中期分析

- **`yearlyGeneration`** (Number): 年发电量
  - 业务含义: 当年累计发电量
  - 单位: 兆瓦时(MWh)
  - 计算方式: 年内日发电量累加
  - 使用场景: 年度报表、长期分析

- **`totalGeneration`** (Number): 累计发电量
  - 业务含义: 电站投运以来的总发电量
  - 单位: 兆瓦时(MWh)
  - 计算方式: 历史发电量累加
  - 使用场景: 总体统计、投资回报分析

##### 运行参数字段
- **`efficiency`** (Number): 发电效率
  - 业务含义: 当日的发电效率
  - 单位: 百分比(%)
  - 数据范围: 0-100
  - 使用场景: 性能分析、设备评估

- **`weather`** (String): 天气状况
  - 业务含义: 当日的天气情况
  - 枚举值:
    - `sunny`: 晴天
    - `cloudy`: 多云
    - `rainy`: 雨天
    - `snowy`: 雪天
  - 使用场景: 发电量影响因素分析

- **`notes`** (String): 备注信息
  - 业务含义: 当日发电的特殊情况说明
  - 使用场景: 异常情况记录、数据解释
  - 数据示例: "设备运行正常"、"上午停机检修"

## 数据关系图

### 核心关系
```
employees (员工)
├── homeStationId → powerstations (归属电站)
├── currentStationId → powerstations (当前电站)
└── 被引用于:
    ├── stationpositions.employeeId (主岗位)
    ├── concurrentpositions.employeeId (兼职岗位)
    └── dispatchrecords.employeeId (调度记录)

powerstations (电站)
└── 被引用于:
    ├── employees.homeStationId (员工归属)
    ├── employees.currentStationId (员工当前)
    ├── stationpositions.stationId (岗位配置)
    ├── concurrentpositions.stationId (兼职岗位)
    ├── dispatchrecords.fromStationId (调出电站)
    ├── dispatchrecords.toStationId (调入电站)
    └── powergeneration.stationId (发电数据)

positions (岗位模板)
└── 被引用于:
    ├── stationpositions.positionId (主岗位)
    └── concurrentpositions.positionId (兼职岗位)

users (用户)
└── 被引用于:
    └── dispatchrecords.approvedBy (审批人)
```

### 业务流程关系
1. **人员管理流程**: employees → stationpositions → concurrentpositions
2. **调度管理流程**: dispatchrecords → employees → powerstations
3. **权限控制流程**: users → role-based access control
4. **数据统计流程**: powergeneration → powerstations → aggregation

## 索引优化策略

### 查询性能优化
1. **高频查询索引**:
   - employees: homeStationId, currentStationId, status
   - stationpositions: stationId + status
   - dispatchrecords: employeeId, status, startDate
   - powergeneration: stationId + date

2. **复合索引设计**:
   - employees: {currentStationId: 1, status: 1, department: 1}
   - dispatchrecords: {status: 1, startDate: 1, endDate: 1}
   - powergeneration: {stationId: 1, date: -1}

3. **文本搜索索引**:
   - employees: name (文本索引)
   - powerstations: name (文本索引)

### 数据一致性保证

#### 引用完整性
1. 所有外键字段必须引用存在的文档
2. 删除操作需要检查引用关系
3. 批量操作需要事务支持

#### 业务规则约束
1. 员工的currentStationId变更需要同步调度记录
2. 岗位分配不能超过岗位数量限制
3. 调度时间不能冲突
4. 发电量数据不能为负值

#### 数据同步机制
1. 员工调度后自动更新currentStationId
2. 岗位变更后自动更新相关统计
3. 电站状态变更后影响人员调度
4. 定期数据一致性检查

## 安全和隐私保护

### 敏感数据保护
1. **密码安全**: bcrypt加密存储，不可逆
2. **个人信息**: phone、email等需要权限控制
3. **操作日志**: 记录所有敏感操作
4. **数据脱敏**: 非生产环境数据脱敏

### 访问控制
1. **角色权限**: 基于role字段的权限控制
2. **数据范围**: 用户只能访问授权范围内的数据
3. **操作审计**: 记录所有数据变更操作
4. **会话管理**: JWT token有效期控制

## 性能监控和优化

### 监控指标
1. **查询性能**: 慢查询监控和优化
2. **索引效率**: 索引使用率和命中率
3. **数据增长**: 表大小和增长趋势
4. **并发性能**: 高并发场景下的性能表现

### 优化建议
1. **分页查询**: 大数据量查询使用分页
2. **缓存策略**: 热点数据使用缓存
3. **批量操作**: 减少数据库交互次数
4. **定期维护**: 索引重建和数据清理

这份详细的数据库字典文档涵盖了系统的所有核心表结构、字段含义、业务规则和技术实现细节，为开发、运维和业务人员提供了全面的参考资料。