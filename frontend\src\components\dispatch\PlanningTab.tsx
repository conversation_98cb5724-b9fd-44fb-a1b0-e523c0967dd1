import React from 'react';
import { Icons } from '../../constants/index';

const PlanningTab: React.FC = () => {
  return (
    <div className="bg-white p-6 rounded-lg shadow">
      <h3 className="text-lg font-semibold mb-6 flex items-center">
        <Icons.Calendar className="w-5 h-5 mr-2" />
        调度计划
      </h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* 项目调度计划 */}
        <div className="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
          <div className="flex items-center mb-4">
            <Icons.Building className="w-6 h-6 text-blue-600 mr-3" />
            <h4 className="text-lg font-medium">项目调度计划</h4>
          </div>
          <p className="text-gray-600 mb-4">
            基于项目需求创建调度计划，自动匹配合适的员工
          </p>
          <button className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors">
            创建项目调度
          </button>
        </div>

        {/* 人员调度计划 */}
        <div className="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
          <div className="flex items-center mb-4">
            <Icons.Users className="w-6 h-6 text-green-600 mr-3" />
            <h4 className="text-lg font-medium">人员调度计划</h4>
          </div>
          <p className="text-gray-600 mb-4">
            基于人员技能和可用性创建调度计划
          </p>
          <button className="w-full bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors">
            创建人员调度
          </button>
        </div>
      </div>

      {/* 计划列表 */}
      <div className="mt-8">
        <h4 className="text-md font-medium mb-4">当前计划</h4>
        <div className="bg-gray-50 rounded-lg p-4 text-center text-gray-500">
          暂无调度计划，点击上方按钮创建新计划
        </div>
      </div>
    </div>
  );
};

export default PlanningTab;