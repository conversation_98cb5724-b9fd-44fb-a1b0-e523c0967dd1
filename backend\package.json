{"name": "power-station-management-backend", "version": "1.0.0", "description": "电站运维管理系统后端API", "main": "dist/index.js", "scripts": {"dev": "nodemon src/app.ts", "build": "tsc", "start": "node dist/app.js", "test": "jest", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "init-db:new": "ts-node src/scripts/initNewDatabase.ts", "seed:dispatch": "ts-node src/scripts/seedDispatchDashboard.ts"}, "dependencies": {"axios": "^1.6.0", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "date-fns": "^3.0.0", "dotenv": "^16.3.0", "express": "^4.18.0", "express-rate-limit": "^7.1.0", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.0", "mongoose": "^8.0.0", "morgan": "^1.10.0", "socket.io": "^4.8.1"}, "devDependencies": {"@types/bcryptjs": "^2.4.0", "@types/compression": "^1.7.0", "@types/cors": "^2.8.0", "@types/express": "^4.17.0", "@types/jsonwebtoken": "^9.0.0", "@types/morgan": "^1.9.0", "@types/node": "^20.10.0", "@types/socket.io": "^3.0.1", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.45.0", "jest": "^29.7.0", "nodemon": "^3.0.0", "ts-jest": "^29.1.0", "ts-node": "^10.9.0", "typescript": "^5.3.0"}, "keywords": ["power-station", "management", "api", "mongodb", "express"], "author": "AI Assistant", "license": "MIT"}