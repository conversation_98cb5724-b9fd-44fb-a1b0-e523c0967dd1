import express from 'express';
import {
  getWorkOrders,
  getWorkOrderById,
  createWorkOrder,
  updateWorkOrder,
  deleteWorkOrder,
  getWorkOrderStats
} from '../controllers/workOrderController';
import { authenticateToken } from '../middleware/auth';

const router = express.Router();

// 所有工单路由都需要认证
router.use(authenticateToken);

// 获取工单统计
router.get('/stats', getWorkOrderStats);

// 获取所有工单
router.get('/', getWorkOrders);

// 根据ID获取工单
router.get('/:id', getWorkOrderById);

// 创建工单
router.post('/', createWorkOrder);

// 更新工单
router.put('/:id', updateWorkOrder);

// 删除工单
router.delete('/:id', deleteWorkOrder);

export default router;
