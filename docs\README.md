# 电站人员调度管理系统

## 项目概述

电站人员调度管理系统是一个基于 Node.js + React 的全栈 Web 应用，用于管理电力企业的人员调度、岗位分配和电站运营数据。

## 技术栈

### 后端
- **框架**: Node.js + Express.js + TypeScript
- **数据库**: MongoDB
- **认证**: JWT (JSON Web Token)
- **API文档**: 内置 RESTful API
- **实时通信**: WebSocket

### 前端
- **框架**: React 18 + TypeScript
- **构建工具**: Vite
- **UI组件**: 自定义组件库
- **状态管理**: React Hooks
- **路由**: React Router

## 数据库配置

### 数据库名称
- **生产数据库**: `power_station_management`
- **连接地址**: `mongodb://localhost:27017/power_station_management`

### 数据库结构
系统包含以下主要数据表：
- **employees** - 员工信息表
- **powerstations** - 电站信息表
- **positions** - 岗位模板表
- **stationpositions** - 电站岗位配置表
- **concurrentpositions** - 兼职岗位表
- **users** - 系统用户表
- **powergeneration** - 发电量数据表
- **dispatchrecords** - 调度记录表

详细的数据库字段说明请参考 [数据库字典文档](../backend/docs/database-schema.md)。

## 项目结构

```
dispatch/
├── frontend/                 # 前端应用
│   ├── src/
│   │   ├── components/      # React 组件
│   │   ├── pages/          # 页面组件
│   │   ├── hooks/          # 自定义 Hooks
│   │   ├── utils/          # 工具函数
│   │   └── types/          # TypeScript 类型定义
│   ├── public/             # 静态资源
│   └── package.json
├── backend/                 # 后端应用
│   ├── src/
│   │   ├── controllers/    # 控制器
│   │   ├── models/         # 数据模型
│   │   ├── routes/         # 路由定义
│   │   ├── middleware/     # 中间件
│   │   ├── config/         # 配置文件
│   │   ├── scripts/        # 数据库脚本
│   │   └── utils/          # 工具函数
│   ├── docs/               # 后端文档
│   └── package.json
└── docs/                   # 项目文档
    └── README.md           # 本文件
```

## 快速开始

### 环境要求
- Node.js 18+
- MongoDB 5.0+
- npm 或 yarn

### 安装依赖

```bash
# 安装后端依赖
cd backend
npm install

# 安装前端依赖
cd ../frontend
npm install
```

### 环境配置

在 `backend` 目录下创建 `.env` 文件：

```env
# 服务器配置
PORT=3002
NODE_ENV=development

# 数据库配置
DB_NAME=power_station_management
MONGODB_URI=mongodb://localhost:27017/power_station_management

# JWT 配置
JWT_SECRET=your_jwt_secret_key_here
JWT_EXPIRES_IN=7d

# CORS 配置
CORS_ORIGIN=http://localhost:5173

# 日志配置
LOG_LEVEL=info

# 限流配置
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
```

### 数据库初始化

```bash
# 进入后端目录
cd backend

# 初始化数据库结构和种子数据
npm run init-db

# 检查数据库状态
npm run check-db

# 创建默认用户
npm run create-users
`| 角色 | 用户名 | 密码 | 权限 |
|------|--------|------|------|
| 管理员 | admin | admin123 | 全部权限 |
| 操作员 | operator | operator123 | 读写权限 |
| 查看者 | viewer | viewer123 | 只读权限 |`

### 启动应用

```bash
# 启动后端服务 (端口 3002)
cd backend
npm run dev

# 启动前端服务 (端口 5173)
cd frontend
npm run dev
```

访问 `http://localhost:5173` 查看应用。

## 主要功能

### 1. 用户管理
- 用户注册/登录
- 角色权限控制 (管理员/操作员/查看者)
- 用户资料管理

### 2. 电站管理
- 电站信息维护
- 电站状态监控
- 发电量数据管理
- 电站地理位置管理

### 3. 员工管理
- 员工档案管理
- 技能证书管理
- 员工状态跟踪
- 归属电站管理

### 4. 岗位管理
- 岗位模板配置
- 电站岗位设置
- 主岗位分配
- 兼职岗位管理

### 5. 调度管理
- 人员调度申请
- 调度审批流程
- 调度记录查询
- 调度状态跟踪

### 6. 数据统计
- 人员分布统计
- 岗位配置统计
- 调度情况分析
- 发电量趋势分析

## API 文档

系统提供完整的 RESTful API，主要端点包括：

- **认证**: `/api/v1/auth/*`
- **电站**: `/api/v1/stations/*`
- **员工**: `/api/v1/employees/*`
- **岗位**: `/api/v1/positions/*`
- **调度**: `/api/v1/dispatch/*`
- **统计**: `/api/v1/stats/*`

详细的 API 文档请参考后端代码中的路由定义。

## 开发指南

### 代码规范
- 使用 TypeScript 进行类型安全开发
- 遵循 ESLint 代码规范
- 使用 Prettier 进行代码格式化

### 数据库操作
- 使用 Mongoose ODM 进行数据库操作
- 所有模型定义在 `backend/src/models/` 目录
- 数据库脚本位于 `backend/src/scripts/` 目录

### 前端开发
- 使用函数式组件和 Hooks
- 组件按功能模块组织
- 使用 TypeScript 接口定义数据类型

## 部署说明

### 生产环境配置
1. 设置正确的环境变量
2. 配置 MongoDB 连接
3. 设置反向代理 (Nginx)
4. 配置 SSL 证书
5. 设置日志轮转

### 数据备份
建议定期备份 MongoDB 数据：

```bash
# 备份数据库
mongodump --db power_station_management --out /path/to/backup

# 恢复数据库
mongorestore --db power_station_management /path/to/backup/power_station_management
```

## 故障排除

### 常见问题
1. **数据库连接失败**: 检查 MongoDB 服务状态和连接字符串
2. **端口冲突**: 修改 `.env` 文件中的端口配置
3. **权限问题**: 确认用户角色和权限设置
4. **数据不一致**: 运行数据库检查脚本

### 日志查看
- 后端日志: 控制台输出或日志文件
- 前端日志: 浏览器开发者工具
- 数据库日志: MongoDB 日志文件

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交代码变更
4. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证。

## 联系方式

如有问题或建议，请联系项目维护者。