import mongoose, { Schema, Document } from 'mongoose';

// 调度类型枚举
export enum DispatchType {
  TEMPORARY = 'temporary',    // 临时调度
  PERMANENT = 'permanent',    // 永久调度
  EMERGENCY = 'emergency'     // 紧急调度
}

// 调度状态枚举
export enum DispatchStatus {
  PENDING = 'pending',        // 待审批
  APPROVED = 'approved',      // 已审批
  ACTIVE = 'active',          // 执行中
  COMPLETED = 'completed',    // 已完成
  CANCELLED = 'cancelled',    // 已取消
  REJECTED = 'rejected'       // 已拒绝
}

// 调度申请来源
export enum DispatchSource {
  MANUAL = 'manual',          // 手动创建
  AI_QUERY = 'ai_query',      // AI查询生成
  AI_RECOMMEND = 'ai_recommend' // AI智能推荐
}

// 风险等级
export enum RiskLevel {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

// 风险分析接口
export interface RiskAnalysis {
  level: RiskLevel;
  description: string;
  affectedPositions: string[];
  remainingStaff: {
    total: number;
    interns: number;
    experienced: number;
  };
  recommendations: string[];
}

// 调度记录接口
export interface IDispatchRecord extends Document {
  _id: string;
  employeeId: mongoose.Types.ObjectId;
  fromStationId: mongoose.Types.ObjectId;
  toStationId: mongoose.Types.ObjectId;
  dispatchType: DispatchType;
  startDate: Date;
  endDate?: Date;
  reason: string;
  status: DispatchStatus;
  source: DispatchSource;
  
  // AI相关字段
  aiQuery?: string;           // AI查询的自然语言
  aiQuerySql?: string;        // AI生成的SQL查询
  aiRecommendationReason?: string; // AI推荐理由
  
  // 审核相关
  reviewerId?: mongoose.Types.ObjectId;
  reviewDate?: Date;
  reviewComments?: string;
  
  // 风险分析
  riskAnalysis?: RiskAnalysis;
  
  // 批量调度相关
  batchId?: string;           // 批量调度ID
  isBatch: boolean;           // 是否为批量调度
  
  createdAt: Date;
  updatedAt: Date;
}

// 调度记录Schema
const DispatchRecordSchema: Schema = new Schema({
  employeeId: {
    type: Schema.Types.ObjectId,
    ref: 'Employee',
    required: true
  },
  fromStationId: {
    type: Schema.Types.ObjectId,
    ref: 'PowerStation',
    required: true
  },
  toStationId: {
    type: Schema.Types.ObjectId,
    ref: 'PowerStation',
    required: true
  },
  dispatchType: {
    type: String,
    enum: Object.values(DispatchType),
    required: true,
    default: DispatchType.TEMPORARY
  },
  startDate: {
    type: Date,
    required: true
  },
  endDate: {
    type: Date
  },
  reason: {
    type: String,
    required: true,
    maxlength: 500
  },
  status: {
    type: String,
    enum: Object.values(DispatchStatus),
    required: true,
    default: DispatchStatus.PENDING
  },
  source: {
    type: String,
    enum: Object.values(DispatchSource),
    required: true,
    default: DispatchSource.MANUAL
  },
  
  // AI相关字段
  aiQuery: {
    type: String,
    maxlength: 1000
  },
  aiQuerySql: {
    type: String,
    maxlength: 2000
  },
  aiRecommendationReason: {
    type: String,
    maxlength: 1000
  },
  
  // 审核相关
  reviewerId: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  reviewDate: {
    type: Date
  },
  reviewComments: {
    type: String,
    maxlength: 500
  },
  
  // 风险分析
  riskAnalysis: {
    level: {
      type: String,
      enum: Object.values(RiskLevel)
    },
    description: {
      type: String,
      maxlength: 500
    },
    affectedPositions: [{
      type: String
    }],
    remainingStaff: {
      total: { type: Number, default: 0 },
      interns: { type: Number, default: 0 },
      experienced: { type: Number, default: 0 }
    },
    recommendations: [{
      type: String,
      maxlength: 200
    }]
  },
  
  // 批量调度相关
  batchId: {
    type: String,
    index: true
  },
  isBatch: {
    type: Boolean,
    default: false
  }
}, {
  timestamps: true,
  collection: 'dispatchrecords'
});

// 索引
DispatchRecordSchema.index({ employeeId: 1, status: 1 });
DispatchRecordSchema.index({ fromStationId: 1, toStationId: 1 });
DispatchRecordSchema.index({ startDate: 1, endDate: 1 });
DispatchRecordSchema.index({ batchId: 1 });
DispatchRecordSchema.index({ status: 1, createdAt: -1 });

// 虚拟字段
DispatchRecordSchema.virtual('employee', {
  ref: 'Employee',
  localField: 'employeeId',
  foreignField: '_id',
  justOne: true
});

DispatchRecordSchema.virtual('fromStation', {
  ref: 'PowerStation',
  localField: 'fromStationId',
  foreignField: '_id',
  justOne: true
});

DispatchRecordSchema.virtual('toStation', {
  ref: 'PowerStation',
  localField: 'toStationId',
  foreignField: '_id',
  justOne: true
});

DispatchRecordSchema.virtual('reviewer', {
  ref: 'User',
  localField: 'reviewerId',
  foreignField: '_id',
  justOne: true
});

// 确保虚拟字段在JSON序列化时包含
DispatchRecordSchema.set('toJSON', { virtuals: true });
DispatchRecordSchema.set('toObject', { virtuals: true });

export default mongoose.model<IDispatchRecord>('DispatchRecord', DispatchRecordSchema);