import React, { useState, useEffect } from 'react';
import { Employee } from '../../types';
import { Icons } from '../../constants/index';

interface DispatchModalProps {
  isOpen: boolean;
  employee: Employee | null;
  stations: any[];
  onClose: () => void;
  onSubmit: (dispatchData: DispatchFormData) => void;
}

interface DispatchFormData {
  employeeId: string;
  fromStationId: string;
  toStationId: string;
  dispatchType: 'temporary' | 'permanent' | 'emergency';
  startDate: string;
  endDate?: string;
  reason: string;
  urgencyLevel: 'low' | 'medium' | 'high' | 'emergency';
  notes?: string;
}

const DispatchModal: React.FC<DispatchModalProps> = ({
  isOpen,
  employee,
  stations,
  onClose,
  onSubmit
}) => {
  const [formData, setFormData] = useState<DispatchFormData>({
    employeeId: '',
    fromStationId: '',
    toStationId: '',
    dispatchType: 'temporary',
    startDate: '',
    endDate: '',
    reason: '',
    urgencyLevel: 'medium',
    notes: ''
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  // 当员工信息变化时，更新表单数据
  useEffect(() => {
    if (employee && isOpen) {
      console.log('更新调度表单数据，员工信息:', employee);
      
      // 获取员工当前电站ID
      const getCurrentStationId = () => {
        // 优先使用 populate 后的对象数据
        if ((employee.currentStationId as any)?._id) {
          return (employee.currentStationId as any)._id;
        }
        if ((employee.currentStationId as any)?.id) {
          return (employee.currentStationId as any).id;
        }
        // 如果是字符串 ID
        if (typeof employee.currentStationId === 'string') {
          return employee.currentStationId;
        }
        // 尝试归属电站
        if ((employee.homeStationId as any)?._id) {
          return (employee.homeStationId as any)._id;
        }
        if ((employee.homeStationId as any)?.id) {
          return (employee.homeStationId as any).id;
        }
        if (typeof employee.homeStationId === 'string') {
          return employee.homeStationId;
        }
        return '';
      };

      const getHomeStationId = () => {
        // 优先使用 populate 后的对象数据
        if ((employee.homeStationId as any)?._id) {
          return (employee.homeStationId as any)._id;
        }
        if ((employee.homeStationId as any)?.id) {
          return (employee.homeStationId as any).id;
        }
        // 如果是字符串 ID
        if (typeof employee.homeStationId === 'string') {
          return employee.homeStationId;
        }
        return '';
      };

      const currentStationId = getCurrentStationId();
      const homeStationId = getHomeStationId();
      
      // 判断员工是否被调度（比较归属电站和当前电站）
      const isDispatched = homeStationId !== currentStationId;
      
      // 根据员工状态预设调度类型
      let defaultDispatchType: 'temporary' | 'permanent' | 'emergency' = 'temporary';
      let defaultUrgencyLevel: 'low' | 'medium' | 'high' | 'emergency' = 'medium';
      
      if (isDispatched) {
        // 如果员工已被调度，默认为调回
        defaultDispatchType = 'temporary';
        defaultUrgencyLevel = 'low';
      } else if (employee.status === 'learning') {
        // 学习状态的员工，可能需要紧急调度
        defaultDispatchType = 'emergency';
        defaultUrgencyLevel = 'high';
      }
      
      // 设置默认开始日期为明天
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      const defaultStartDate = tomorrow.toISOString().split('T')[0];
      
      // 设置默认结束日期为一个月后
      const oneMonthLater = new Date();
      oneMonthLater.setMonth(oneMonthLater.getMonth() + 1);
      const defaultEndDate = oneMonthLater.toISOString().split('T')[0];
      
      setFormData({
        employeeId: employee.id,
        fromStationId: currentStationId,
        toStationId: isDispatched ? homeStationId : '',
        dispatchType: defaultDispatchType,
        startDate: defaultStartDate,
        endDate: defaultEndDate,
        reason: isDispatched ? '调回原电站' : '',
        urgencyLevel: defaultUrgencyLevel,
        notes: ''
      });
      
      console.log('设置表单数据:', {
        employeeId: employee.id,
        fromStationId: currentStationId,
        toStationId: isDispatched ? homeStationId : '',
        dispatchType: defaultDispatchType,
        urgencyLevel: defaultUrgencyLevel,
        isDispatched
      });
    }
  }, [employee, isOpen]);

  // 表单验证
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.toStationId) {
      newErrors.toStationId = '请选择目标电站';
    }
    if (!formData.startDate) {
      newErrors.startDate = '请选择开始日期';
    }
    if (!formData.reason.trim()) {
      newErrors.reason = '请填写调度原因';
    }
    if (formData.dispatchType === 'temporary' && !formData.endDate) {
      newErrors.endDate = '临时调度必须设置结束日期';
    }
    if (formData.endDate && formData.startDate && new Date(formData.endDate) <= new Date(formData.startDate)) {
      newErrors.endDate = '结束日期必须晚于开始日期';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 处理表单提交
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      onSubmit(formData);
      handleClose();
    }
  };

  // 关闭模态框并重置表单
  const handleClose = () => {
    setFormData({
      employeeId: '',
      fromStationId: '',
      toStationId: '',
      dispatchType: 'temporary',
      startDate: '',
      endDate: '',
      reason: '',
      urgencyLevel: 'medium',
      notes: ''
    });
    setErrors({});
    onClose();
  };

  // 获取电站名称
  const getStationName = (stationId: string) => {
    const station = stations.find(s => s.id === stationId);
    return station ? station.name : '未知电站';
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-slate-900 border border-cyan-400/30 rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        {/* 模态框头部 */}
        <div className="flex items-center justify-between p-6 border-b border-gray-600">
          <h2 className="text-xl font-semibold text-white flex items-center">
            <Icons.Calendar className="w-6 h-6 mr-2 text-cyan-400" />
            创建调度申请
          </h2>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-300 transition-colors"
          >
            <Icons.X className="w-6 h-6" />
          </button>
        </div>

        {/* 员工信息 */}
        {employee && (
          <div className="p-6 bg-slate-800/50 border-b border-gray-600">
            <h3 className="text-lg font-medium text-white mb-3">员工信息</h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <span className="text-sm text-gray-400">姓名：</span>
                <span className="font-medium text-white">{employee.name}</span>
              </div>
              <div>
                <span className="text-sm text-gray-400">岗位：</span>
                <span className="font-medium text-white">
                  {employee.position || '未分配'}
                </span>
              </div>
              <div>
                <span className="text-sm text-gray-400">当前电站：</span>
                <span className="font-medium text-white">
                  {(() => {
                    // 优先使用 populate 后的对象数据
                    if ((employee.currentStationId as any)?.name) {
                      return (employee.currentStationId as any).name;
                    }
                    if (employee.currentStation?.name) {
                      return employee.currentStation.name;
                    }
                    // 如果是字符串 ID，从 stations 列表中查找
                    if (typeof employee.currentStationId === 'string') {
                      const station = stations.find(s => s.id === employee.currentStationId);
                      if (station) return station.name;
                    }
                    // 尝试归属电站
                    if ((employee.homeStationId as any)?.name) {
                      return (employee.homeStationId as any).name;
                    }
                    if (employee.homeStation?.name) {
                      return employee.homeStation.name;
                    }
                    if (typeof employee.homeStationId === 'string') {
                      const station = stations.find(s => s.id === employee.homeStationId);
                      if (station) return station.name;
                    }
                    return '未知';
                  })()}
                </span>
              </div>
              <div>
                <span className="text-sm text-gray-400">工作经验：</span>
                <span className="font-medium text-white">{employee.experience || 0}年</span>
              </div>
            </div>
          </div>
        )}

        {/* 表单内容 */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* 调度类型和紧急程度 */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                调度类型 <span className="text-red-400">*</span>
              </label>
              <select
                value={formData.dispatchType}
                onChange={(e) => setFormData(prev => ({ 
                  ...prev, 
                  dispatchType: e.target.value as any,
                  endDate: e.target.value === 'permanent' ? '' : prev.endDate
                }))}
                className="w-full px-3 py-2 bg-slate-800 border border-gray-600 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-cyan-500"
              >
                <option value="temporary">临时调度</option>
                <option value="permanent">永久调度</option>
                <option value="emergency">紧急调度</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                紧急程度 <span className="text-red-400">*</span>
              </label>
              <select
                value={formData.urgencyLevel}
                onChange={(e) => setFormData(prev => ({ ...prev, urgencyLevel: e.target.value as any }))}
                className="w-full px-3 py-2 bg-slate-800 border border-gray-600 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-cyan-500"
              >
                <option value="low">低</option>
                <option value="medium">中</option>
                <option value="high">高</option>
                <option value="emergency">紧急</option>
              </select>
            </div>
          </div>

          {/* 目标电站 */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              目标电站 <span className="text-red-400">*</span>
            </label>
            <select
              value={formData.toStationId}
              onChange={(e) => setFormData(prev => ({ ...prev, toStationId: e.target.value }))}
              className={`w-full px-3 py-2 bg-slate-800 border text-white rounded-md focus:outline-none focus:ring-2 focus:ring-cyan-500 ${
                errors.toStationId ? 'border-red-500 focus:border-red-500' : 'border-gray-600 focus:border-cyan-500'
              }`}
            >
              <option value="">请选择目标电站</option>
              {stations
                .filter(station => station.id !== formData.fromStationId)
                .map(station => (
                  <option key={station.id} value={station.id}>
                    {station.name}
                  </option>
                ))
              }
            </select>
            {errors.toStationId && (
              <p className="mt-1 text-sm text-red-400">{errors.toStationId}</p>
            )}
          </div>

          {/* 时间设置 */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                开始日期 <span className="text-red-400">*</span>
              </label>
              <input
                type="date"
                value={formData.startDate}
                onChange={(e) => setFormData(prev => ({ ...prev, startDate: e.target.value }))}
                min={new Date().toISOString().split('T')[0]}
                className={`w-full px-3 py-2 bg-slate-800 border text-white rounded-md focus:outline-none focus:ring-2 focus:ring-cyan-500 ${
                  errors.startDate ? 'border-red-500 focus:border-red-500' : 'border-gray-600 focus:border-cyan-500'
                }`}
              />
              {errors.startDate && (
                <p className="mt-1 text-sm text-red-400">{errors.startDate}</p>
              )}
            </div>
            
            {formData.dispatchType !== 'permanent' && (
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  结束日期 {formData.dispatchType === 'temporary' && <span className="text-red-400">*</span>}
                </label>
                <input
                  type="date"
                  value={formData.endDate}
                  onChange={(e) => setFormData(prev => ({ ...prev, endDate: e.target.value }))}
                  min={formData.startDate || new Date().toISOString().split('T')[0]}
                  className={`w-full px-3 py-2 bg-slate-800 border text-white rounded-md focus:outline-none focus:ring-2 focus:ring-cyan-500 ${
                    errors.endDate ? 'border-red-500 focus:border-red-500' : 'border-gray-600 focus:border-cyan-500'
                  }`}
                />
                {errors.endDate && (
                  <p className="mt-1 text-sm text-red-400">{errors.endDate}</p>
                )}
              </div>
            )}
          </div>

          {/* 调度原因 */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              调度原因 <span className="text-red-400">*</span>
            </label>
            <textarea
              value={formData.reason}
              onChange={(e) => setFormData(prev => ({ ...prev, reason: e.target.value }))}
              rows={3}
              placeholder="请详细说明调度原因..."
              className={`w-full px-3 py-2 bg-slate-800 border text-white rounded-md focus:outline-none focus:ring-2 focus:ring-cyan-500 resize-none placeholder-gray-400 ${
                errors.reason ? 'border-red-500 focus:border-red-500' : 'border-gray-600 focus:border-cyan-500'
              }`}
            />
            {errors.reason && (
              <p className="mt-1 text-sm text-red-400">{errors.reason}</p>
            )}
          </div>

          {/* 备注 */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              备注信息
            </label>
            <textarea
              value={formData.notes}
              onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
              rows={2}
              placeholder="其他需要说明的信息..."
              className="w-full px-3 py-2 bg-slate-800 border border-gray-600 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-cyan-500 resize-none placeholder-gray-400"
            />
          </div>

          {/* 按钮组 */}
          <div className="flex justify-end space-x-3 pt-4 border-t border-gray-600">
            <button
              type="button"
              onClick={handleClose}
              className="px-4 py-2 text-sm font-medium text-gray-300 bg-slate-700 border border-gray-600 rounded-md hover:bg-slate-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
            >
              取消
            </button>
            <button
              type="submit"
              className="px-4 py-2 text-sm font-medium text-white bg-cyan-600 border border-transparent rounded-md hover:bg-cyan-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-cyan-500"
            >
              创建调度申请
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default DispatchModal;