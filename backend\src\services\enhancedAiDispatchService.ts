import { Employee, PowerStation, DispatchRecord } from '../models';
import { 
  RecommendationConfig, 
  EmployeeBusinessStatus, 
  EnhancedRecommendation, 
  RecommendationRequest, 
  RecommendationResponse 
} from '../types/aiRecommendationTypes';
import { config } from '../config';
import axios from 'axios';

// AI调度服务接口定义
export interface AIDispatchService {
  generateDispatchQuery(requirement: string, urgency: string): Promise<string>;
  analyzeDispatchRisk(dispatchData: any): Promise<any>;
  getAIRecommendations(requirement: string, employees: any[], stations: any[]): Promise<any>;
}

// 调度风险分析结果接口
export interface DispatchRiskAnalysis {
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  riskScore: number;
  riskFactors: string[];
  recommendations: string[];
  stationImpact: {
    stationId: string;
    stationName: string;
    beforeDispatch: {
      totalStaff: number;
      experiencedStaff: number;
      interns: number;
      criticalPositions: { position: string; count: number }[];
    };
    afterDispatch: {
      totalStaff: number;
      experiencedStaff: number;
      interns: number;
      criticalPositions: { position: string; count: number }[];
    };
    riskLevel: 'low' | 'medium' | 'high' | 'critical';
    suggestions: string[];
  }[];
  overallAssessment: string;
}

/**
 * 调用阿里云百炼大模型
 */
const callQwenAPI = async (systemPrompt: string, userPrompt: string, temperature: number = 0.1): Promise<string> => {
  try {
    const response = await axios.post(
      `${config.dashscope.baseUrl}/services/aigc/text-generation/generation`,
      {
        model: 'qwen-plus',
        input: {
          messages: [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: userPrompt }
          ]
        },
        parameters: {
          temperature,
          top_p: 0.8,
          max_tokens: 2000,
          result_format: 'message'
        }
      },
      {
        headers: {
          'Authorization': `Bearer ${config.dashscope.apiKey}`,
          'Content-Type': 'application/json'
        },
        timeout: 30000
      }
    );

    const content = response.data.output?.choices?.[0]?.message?.content;
    if (!content) {
      throw new Error('AI模型返回内容为空');
    }

    return content;
  } catch (error: any) {
    console.error('调用阿里云百炼大模型失败:', error);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', error.response.data);
    }
    throw new Error(`AI模型调用失败: ${error.message || '未知错误'}`);
  }
};

/**
 * 生成调度查询SQL
 */
export const generateDispatchQuery = async (requirement: string, urgency: string = 'medium'): Promise<string> => {
  try {
    const systemPrompt = `你是一个专业的数据库查询专家，专门为电力调度系统生成SQL查询语句。

数据库表结构：
1. employees表：
   - _id: ObjectId (员工ID)
   - name: String (姓名)
   - position: String (职位)
   - department: String (部门)
   - employeeType: String (员工类型: 'regular', 'intern', 'contractor')
   - skills: Array<String> (技能列表)
   - certifications: Array<String> (证书列表)
   - currentStationId: ObjectId (当前电站ID)
   - homeStationId: ObjectId (家乡电站ID)
   - status: String (状态: 'active', 'inactive', 'on_leave')
   - hireDate: Date (入职日期)

2. powerstations表：
   - _id: ObjectId (电站ID)
   - name: String (电站名称)
   - location: String (位置)
   - type: String (类型)
   - capacity: Number (容量)

3. dispatchrecords表：
   - _id: ObjectId (调度记录ID)
   - employeeId: ObjectId (员工ID)
   - fromStationId: ObjectId (源电站ID)
   - toStationId: ObjectId (目标电站ID)
   - startDate: Date (开始日期)
   - endDate: Date (结束日期)
   - status: String (状态: 'pending', 'approved', 'active', 'completed', 'cancelled')
   - reason: String (调度原因)

请根据需求生成MongoDB查询语句（使用Mongoose语法）。`;

    const userPrompt = `需求：${requirement}
紧急程度：${urgency}

请生成相应的查询语句，要求：
1. 使用Mongoose查询语法
2. 考虑员工的技能匹配
3. 考虑员工的可用性（不在调度中）
4. 根据紧急程度调整查询条件
5. 返回纯JavaScript代码，不要包含解释文字`;

    const response = await callQwenAPI(systemPrompt, userPrompt, 0.1);
    
    // 提取代码块
    const codeMatch = response.match(/```(?:javascript|js)?\n?([\s\S]*?)\n?```/);
    if (codeMatch) {
      return codeMatch[1].trim();
    }
    
    // 如果没有代码块，返回整个响应（去除多余空白）
    return response.trim();
    
  } catch (error) {
    console.error('生成调度查询失败:', error);
    // 返回后备查询
    return generateFallbackQuery(requirement, urgency);
  }
};

/**
 * 后备查询生成（当AI调用失败时使用）
 */
const generateFallbackQuery = (requirement: string, urgency: string): string => {
  const baseQuery = `
// 基础员工查询
const employees = await Employee.find({
  status: 'active'
})
.populate('currentStationId')
.populate('homeStationId');

// 过滤可用员工（不在当前调度中）
const activeDispatches = await DispatchRecord.find({
  status: { $in: ['approved', 'active'] },
  endDate: { $gte: new Date() }
});

const busyEmployeeIds = activeDispatches.map(d => d.employeeId);
const availableEmployees = employees.filter(emp => 
  !busyEmployeeIds.includes(emp._id)
);`;

  // 根据需求添加技能过滤
  if (requirement.includes('电工') || requirement.includes('电气')) {
    return baseQuery + `
// 过滤具有电气技能的员工
const qualifiedEmployees = availableEmployees.filter(emp =>
  emp.skills && (
    emp.skills.includes('电工') || 
    emp.skills.includes('电气维护') ||
    emp.skills.includes('电气运维')
  )
);`;
  }

  if (requirement.includes('安全')) {
    return baseQuery + `
// 过滤具有安全技能的员工
const qualifiedEmployees = availableEmployees.filter(emp =>
  emp.skills && (
    emp.skills.includes('安全管理') || 
    emp.skills.includes('安全监督') ||
    emp.certifications && emp.certifications.includes('安全员证')
  )
);`;
  }

  return baseQuery + `
// 返回所有可用员工
const qualifiedEmployees = availableEmployees;`;
};
/**
 * 分析调度风险
 */
export const analyzeDispatchRisk = async (dispatchData: {
  employeeId: string;
  fromStationId: string;
  toStationId: string;
  startDate: Date;
  endDate: Date;
  reason: string;
}): Promise<DispatchRiskAnalysis> => {
  try {
    console.log('🔍 开始调度风险分析');

    // 获取相关数据
    const [employee, fromStation, toStation, allEmployees] = await Promise.all([
      Employee.findById(dispatchData.employeeId).populate('currentStationId'),
      PowerStation.findById(dispatchData.fromStationId),
      PowerStation.findById(dispatchData.toStationId),
      Employee.find({ status: 'active' }).populate('currentStationId')
    ]);

    if (!employee || !fromStation || !toStation) {
      throw new Error('无法找到相关的员工或电站信息');
    }

    // 分析源电站和目标电站的人员配置影响
    const stationImpact = await analyzeStationImpact(
      [dispatchData.fromStationId, dispatchData.toStationId],
      dispatchData.employeeId,
      allEmployees
    );

    // 计算风险因素
    const riskFactors: string[] = [];
    let riskScore = 0;

    // 1. 员工疲劳度风险
    const businessStatus = await getEmployeeBusinessStatus(dispatchData.employeeId);
    if (businessStatus.fatigueScore > 70) {
      riskFactors.push(`员工疲劳度较高(${businessStatus.fatigueScore}/100)`);
      riskScore += 25;
    }

    // 2. 连续调度风险
    if (businessStatus.continuousDispatches >= 3) {
      riskFactors.push(`连续调度次数过多(${businessStatus.continuousDispatches}次)`);
      riskScore += 20;
    }

    // 3. 电站人员配置风险
    const fromStationImpact = stationImpact.find(s => s.stationId === dispatchData.fromStationId);
    const toStationImpact = stationImpact.find(s => s.stationId === dispatchData.toStationId);

    if (fromStationImpact && fromStationImpact.afterDispatch.totalStaff < 3) {
      riskFactors.push(`源电站${fromStationImpact.stationName}人员不足(剩余${fromStationImpact.afterDispatch.totalStaff}人)`);
      riskScore += 30;
    }

    if (fromStationImpact && fromStationImpact.afterDispatch.experiencedStaff < 1) {
      riskFactors.push(`源电站${fromStationImpact.stationName}缺乏经验丰富的员工`);
      riskScore += 25;
    }

    // 4. 关键岗位风险
    const criticalPositions = ['站长', '值长', '安全员'];
    if (criticalPositions.includes((employee as any).position)) {
      riskFactors.push(`调度关键岗位员工(${(employee as any).position})`);
      riskScore += 20;
    }

    // 5. 技能匹配风险
    const requiredSkills = extractSkillsFromRequirement(dispatchData.reason);
    const employeeSkills = (employee as any).skills || [];
    const skillMatch = calculateSkillMatch(employeeSkills, requiredSkills);
    if (skillMatch.score < 60) {
      riskFactors.push(`技能匹配度较低(${Math.round(skillMatch.score)}%)`);
      riskScore += 15;
    }

    // 6. 跨区域调度风险
    if (fromStation.location !== toStation.location) {
      riskFactors.push('跨区域调度，需要考虑交通和住宿安排');
      riskScore += 10;
    }

    // 确定风险等级
    let riskLevel: 'low' | 'medium' | 'high' | 'critical';
    if (riskScore < 30) riskLevel = 'low';
    else if (riskScore < 60) riskLevel = 'medium';
    else if (riskScore < 90) riskLevel = 'high';
    else riskLevel = 'critical';

    // 生成建议
    const recommendations = generateRiskRecommendations(riskFactors, riskLevel, businessStatus);

    // 生成整体评估
    const overallAssessment = await generateOverallAssessment(
      employee,
      fromStation,
      toStation,
      riskLevel,
      riskFactors,
      stationImpact
    );

    return {
      riskLevel,
      riskScore: Math.round(riskScore),
      riskFactors,
      recommendations,
      stationImpact,
      overallAssessment
    };

  } catch (error) {
    console.error('调度风险分析失败:', error);
    throw new Error(`风险分析失败: ${(error as Error).message || '未知错误'}`);
  }
};

/**
 * 分析电站人员配置影响
 */
const analyzeStationImpact = async (
  stationIds: string[],
  dispatchedEmployeeId: string,
  allEmployees: any[]
): Promise<DispatchRiskAnalysis['stationImpact']> => {
  const stationImpact: DispatchRiskAnalysis['stationImpact'] = [];

  for (const stationId of stationIds) {
    const station = await PowerStation.findById(stationId);
    if (!station) continue;

    // 获取当前在该电站的员工
    const stationEmployees = allEmployees.filter(emp => 
      emp.currentStationId && emp.currentStationId._id.toString() === stationId
    );

    // 计算调度前的配置
    const beforeDispatch = calculateStationConfig(stationEmployees);

    // 计算调度后的配置
    let afterDispatchEmployees = [...stationEmployees];
    
    // 如果是源电站，移除被调度的员工
    if (stationEmployees.some(emp => emp._id.toString() === dispatchedEmployeeId)) {
      afterDispatchEmployees = afterDispatchEmployees.filter(emp => 
        emp._id.toString() !== dispatchedEmployeeId
      );
    }
    
    // 如果是目标电站，添加被调度的员工
    const dispatchedEmployee = allEmployees.find(emp => emp._id.toString() === dispatchedEmployeeId);
    if (dispatchedEmployee && !stationEmployees.some(emp => emp._id.toString() === dispatchedEmployeeId)) {
      afterDispatchEmployees.push(dispatchedEmployee);
    }

    const afterDispatch = calculateStationConfig(afterDispatchEmployees);

    // 评估风险等级
    let stationRiskLevel: 'low' | 'medium' | 'high' | 'critical' = 'low';
    const suggestions: string[] = [];

    if (afterDispatch.totalStaff < 2) {
      stationRiskLevel = 'critical';
      suggestions.push('电站人员严重不足，建议暂停调度或安排替补人员');
    } else if (afterDispatch.totalStaff < 3) {
      stationRiskLevel = 'high';
      suggestions.push('电站人员偏少，建议安排临时支援');
    } else if (afterDispatch.experiencedStaff < 1) {
      stationRiskLevel = 'high';
      suggestions.push('缺乏经验丰富的员工，建议安排资深员工指导');
    } else if (afterDispatch.experiencedStaff < 2) {
      stationRiskLevel = 'medium';
      suggestions.push('经验丰富的员工较少，需要加强监督');
    }

    // 检查关键岗位
    const criticalPositionShortage = afterDispatch.criticalPositions.filter(pos => pos.count === 0);
    if (criticalPositionShortage.length > 0) {
      stationRiskLevel = 'critical';
      suggestions.push(`关键岗位空缺：${criticalPositionShortage.map(p => p.position).join('、')}`);
    }

    stationImpact.push({
      stationId,
      stationName: (station as any).name,
      beforeDispatch,
      afterDispatch,
      riskLevel: stationRiskLevel,
      suggestions
    });
  }

  return stationImpact;
};

/**
 * 计算电站人员配置
 */
const calculateStationConfig = (employees: any[]) => {
  const criticalPositions = ['站长', '值长', '安全员', '技术员'];
  
  const config = {
    totalStaff: employees.length,
    experiencedStaff: 0,
    interns: 0,
    criticalPositions: criticalPositions.map(position => ({
      position,
      count: 0
    }))
  };

  employees.forEach(emp => {
    // 计算经验丰富的员工（入职超过2年）
    const hireDate = new Date(emp.hireDate);
    const yearsOfExperience = (Date.now() - hireDate.getTime()) / (365.25 * 24 * 60 * 60 * 1000);
    if (yearsOfExperience >= 2) {
      config.experiencedStaff++;
    }

    // 计算实习生
    if (emp.employeeType === 'intern') {
      config.interns++;
    }

    // 计算关键岗位人员
    const criticalPos = config.criticalPositions.find(pos => pos.position === emp.position);
    if (criticalPos) {
      criticalPos.count++;
    }
  });

  return config;
};

/**
 * 生成风险建议
 */
const generateRiskRecommendations = (
  riskFactors: string[],
  riskLevel: string,
  businessStatus: EmployeeBusinessStatus
): string[] => {
  const recommendations: string[] = [];

  if (riskLevel === 'critical') {
    recommendations.push('建议暂停此次调度，重新评估调度方案');
    recommendations.push('考虑安排其他可用员工或延后调度时间');
  } else if (riskLevel === 'high') {
    recommendations.push('建议谨慎执行，加强监督和支持');
    recommendations.push('准备应急预案，确保人员安全');
  }

  if (businessStatus.fatigueScore > 70) {
    recommendations.push('安排员工适当休息后再执行调度');
    recommendations.push('缩短调度时间或安排轮换');
  }

  if (businessStatus.continuousDispatches >= 3) {
    recommendations.push('考虑安排其他员工，避免过度调度');
  }

  if (riskFactors.some(factor => factor.includes('人员不足'))) {
    recommendations.push('为相关电站安排临时支援人员');
    recommendations.push('调整调度时间，确保人员充足');
  }

  if (riskFactors.some(factor => factor.includes('技能匹配'))) {
    recommendations.push('安排技能培训或配备指导员工');
    recommendations.push('考虑选择技能更匹配的员工');
  }

  return recommendations;
};

/**
 * 生成整体评估
 */
const generateOverallAssessment = async (
  employee: any,
  fromStation: any,
  toStation: any,
  riskLevel: string,
  riskFactors: string[],
  stationImpact: DispatchRiskAnalysis['stationImpact']
): Promise<string> => {
  try {
    const systemPrompt = `你是一个专业的人力资源风险评估专家，专门分析电力调度的风险和影响。

请根据以下信息生成一份简洁的整体风险评估报告：

员工信息：
- 姓名：${employee.name}
- 职位：${employee.position}
- 部门：${employee.department}

调度信息：
- 源电站：${fromStation.name}
- 目标电站：${toStation.name}
- 风险等级：${riskLevel}

风险因素：
${riskFactors.map(factor => `- ${factor}`).join('\n')}

电站影响：
${stationImpact.map(impact => 
  `- ${impact.stationName}: 调度后剩余${impact.afterDispatch.totalStaff}人，风险等级${impact.riskLevel}`
).join('\n')}

请生成一份100-200字的整体评估，包括：
1. 调度可行性评估
2. 主要风险点
3. 总体建议`;

    const userPrompt = '请生成整体风险评估报告';

    const response = await callQwenAPI(systemPrompt, userPrompt, 0.2);
    return response.trim();

  } catch (error) {
    console.error('生成整体评估失败:', error);
    
    // 后备评估
    let assessment = `调度${employee.name}从${fromStation.name}到${toStation.name}的风险等级为${riskLevel}。`;
    
    if (riskLevel === 'critical') {
      assessment += '存在严重风险，不建议执行此次调度。';
    } else if (riskLevel === 'high') {
      assessment += '存在较高风险，需要谨慎执行并加强监督。';
    } else if (riskLevel === 'medium') {
      assessment += '存在一定风险，建议做好预防措施。';
    } else {
      assessment += '风险较低，可以正常执行。';
    }

    if (riskFactors.length > 0) {
      assessment += `主要风险包括：${riskFactors.slice(0, 3).join('、')}。`;
    }

    return assessment;
  }
};

/**
 * 获取AI智能推荐（兼容原有接口）
 */
export const getAIRecommendations = async (
  requirement: string,
  employees: any[],
  stations: any[]
): Promise<any> => {
  try {
    console.log('🤖 开始AI智能推荐分析');

    const systemPrompt = `你是一个专业的人力资源调度专家，专门为电力系统提供员工调度建议。

请根据以下信息分析并推荐最适合的员工：

调度需求：${requirement}

可用员工信息：
${employees.map(emp => `
- 姓名：${emp.name}
- 职位：${emp.position}
- 部门：${emp.department}
- 技能：${emp.skills?.join(', ') || '无'}
- 证书：${emp.certifications?.join(', ') || '无'}
- 当前电站：${emp.currentStationId?.name || '未分配'}
- 员工类型：${emp.employeeType}
`).join('\n')}

电站信息：
${stations.map(station => `
- 电站名称：${station.name}
- 位置：${station.location}
- 类型：${station.type}
`).join('\n')}

请返回JSON格式的推荐结果：
{
  "recommendations": [
    {
      "employeeId": "员工ID",
      "employeeName": "员工姓名",
      "score": 85,
      "reasons": ["推荐理由1", "推荐理由2"],
      "risks": ["风险因素1", "风险因素2"],
      "suggestions": ["建议1", "建议2"]
    }
  ],
  "analysis": "整体分析说明",
  "alternatives": ["备选方案1", "备选方案2"]
}`;

    const userPrompt = '请分析并推荐最适合的员工';

    const response = await callQwenAPI(systemPrompt, userPrompt, 0.2);

    // 尝试解析JSON响应
    try {
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const result = JSON.parse(jsonMatch[0]);
        return {
          success: true,
          data: result,
          message: 'AI推荐生成成功'
        };
      }
    } catch (parseError) {
      console.error('解析AI响应失败:', parseError);
    }

    // 如果解析失败，返回后备推荐
    return getFallbackRecommendations(requirement, employees, stations);

  } catch (error) {
    console.error('AI推荐失败:', error);
    // 返回后备推荐
    return getFallbackRecommendations(requirement, employees, stations);
  }
};

/**
 * 后备推荐方案（当AI调用失败时使用）
 */
const getFallbackRecommendations = (
  requirement: string,
  employees: any[],
  stations: any[]
): any => {
  console.log('🔄 使用后备推荐算法');

  const recommendations = [];
  const requiredSkills = extractSkillsFromRequirement(requirement);

  for (const employee of employees) {
    if (employee.status !== 'active') continue;

    // 计算技能匹配度
    const skillMatch = calculateSkillMatch(employee.skills || [], requiredSkills);
    
    // 计算经验评分
    const hireDate = new Date(employee.hireDate);
    const yearsOfExperience = (Date.now() - hireDate.getTime()) / (365.25 * 24 * 60 * 60 * 1000);
    const experienceScore = Math.min(100, yearsOfExperience * 20);

    // 计算综合评分
    const score = Math.round((skillMatch.score * 0.6) + (experienceScore * 0.4));

    const reasons = [];
    const risks = [];
    const suggestions = [];

    // 生成推荐理由
    if (skillMatch.score > 80) {
      reasons.push('技能高度匹配调度需求');
    } else if (skillMatch.score > 60) {
      reasons.push('技能基本匹配调度需求');
    }

    if (yearsOfExperience > 3) {
      reasons.push('工作经验丰富');
    } else if (yearsOfExperience > 1) {
      reasons.push('具有一定工作经验');
    }

    if (employee.employeeType === 'regular') {
      reasons.push('正式员工，稳定性好');
    }

    // 生成风险因素
    if (skillMatch.score < 60) {
      risks.push('技能匹配度较低，可能需要额外培训');
    }

    if (employee.employeeType === 'intern') {
      risks.push('实习生，需要指导和监督');
    }

    if (yearsOfExperience < 1) {
      risks.push('工作经验较少，需要谨慎安排');
    }

    // 生成建议
    if (risks.length > 0) {
      suggestions.push('建议安排经验丰富的员工指导');
    }

    if (skillMatch.missingSkills.length > 0) {
      suggestions.push(`建议补充技能培训：${skillMatch.missingSkills.join('、')}`);
    }

    recommendations.push({
      employeeId: employee._id.toString(),
      employeeName: employee.name,
      score,
      reasons,
      risks,
      suggestions
    });
  }

  // 按评分排序
  recommendations.sort((a, b) => b.score - a.score);

  // 生成分析
  const topScore = recommendations[0]?.score || 0;
  let analysis = '';
  
  if (topScore > 80) {
    analysis = '找到了高度匹配的候选人，建议优先考虑排名靠前的员工。';
  } else if (topScore > 60) {
    analysis = '找到了基本匹配的候选人，建议结合具体情况选择合适的员工。';
  } else {
    analysis = '当前候选人匹配度较低，建议考虑调整需求条件或寻找其他人选。';
  }

  // 生成备选方案
  const alternatives = [
    '考虑分阶段调度，降低单次调度压力',
    '安排技能培训，提升员工能力匹配度',
    '考虑跨部门调度，扩大候选人范围'
  ];

  if (employees.some(emp => emp.employeeType === 'intern')) {
    alternatives.push('安排实习生参与，提供学习机会');
  }

  return {
    success: true,
    data: {
      recommendations: recommendations.slice(0, 5), // 返回前5名
      analysis,
      alternatives
    },
    message: '后备推荐算法生成成功'
  };
};

const DEFAULT_RECOMMENDATION_CONFIG: RecommendationConfig = {
  // 基础权重
  skillMatchWeight: 30,
  experienceWeight: 25,
  positionMatchWeight: 20,
  locationWeight: 15,
  
  // 疲劳度和连续调度规则
  maxContinuousDispatches: 3,
  minRestDaysBetweenDispatches: 2,
  fatigueThreshold: 70,
  
  // 风险控制
  minStaffRemaining: 2,
  criticalPositionProtection: ['站长', '值长', '安全员'],
  emergencyResponseTime: 4,
  
  // 优先级规则
  preferInternalTransfer: true,
  preferExperiencedStaff: true,
  considerTrainingNeeds: true,
  
  // 特殊情况
  allowOvertimeDispatch: false,
  allowCrossRegionDispatch: true,
  requireManagerApproval: true
};

/**
 * 获取员工业务状态
 */
export const getEmployeeBusinessStatus = async (employeeId: string): Promise<EmployeeBusinessStatus> => {
  try {
    // 获取员工调度历史
    const dispatchHistory = await DispatchRecord.find({
      employeeId,
      createdAt: { $gte: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000) } // 近90天
    }).sort({ createdAt: -1 });

    // 计算调度统计
    const totalDispatches = dispatchHistory.length;
    const recentDispatches = dispatchHistory.filter(
      record => record.createdAt >= new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
    ).length;

    // 计算连续调度次数
    let continuousDispatches = 0;
    const sortedHistory = dispatchHistory.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
    for (const record of sortedHistory) {
      if (record.status === 'completed' || record.status === 'active') {
        continuousDispatches++;
      } else {
        break;
      }
    }

    // 计算疲劳度评分
    const fatigueScore = Math.min(100, (recentDispatches * 15) + (continuousDispatches * 20));
    
    // 计算工作负荷评分
    const workloadScore = Math.min(100, totalDispatches * 5 + recentDispatches * 10);

    // 获取最后调度结束时间
    const lastDispatch = sortedHistory.find(r => r.endDate);
    const lastDispatchEndDate = lastDispatch?.endDate;

    // 判断可用性
    const daysSinceLastDispatch = lastDispatchEndDate 
      ? Math.floor((Date.now() - lastDispatchEndDate.getTime()) / (24 * 60 * 60 * 1000))
      : 999;
    
    const isAvailable = fatigueScore < DEFAULT_RECOMMENDATION_CONFIG.fatigueThreshold && 
                       continuousDispatches < DEFAULT_RECOMMENDATION_CONFIG.maxContinuousDispatches &&
                       daysSinceLastDispatch >= DEFAULT_RECOMMENDATION_CONFIG.minRestDaysBetweenDispatches;

    return {
      employeeId,
      totalDispatches,
      recentDispatches,
      continuousDispatches,
      lastDispatchEndDate,
      fatigueScore,
      workloadScore,
      skillProficiency: {}, // 可以从其他系统获取
      performanceRating: 85, // 默认评分，可以从HR系统获取
      adaptabilityScore: 80, // 默认评分
      isAvailable,
      unavailableReason: isAvailable ? undefined : '疲劳度过高或连续调度次数过多',
      trainingNeeds: [], // 可以从培训系统获取
      careerGoals: [],
      mentorshipRole: 'none'
    };
  } catch (error) {
    console.error('获取员工业务状态失败:', error);
    // 返回默认状态
    return {
      employeeId,
      totalDispatches: 0,
      recentDispatches: 0,
      continuousDispatches: 0,
      fatigueScore: 0,
      workloadScore: 0,
      skillProficiency: {},
      performanceRating: 75,
      adaptabilityScore: 75,
      isAvailable: true,
      trainingNeeds: [],
      careerGoals: [],
      mentorshipRole: 'none'
    };
  }
};

/**
 * 计算技能匹配度
 */
const calculateSkillMatch = (employeeSkills: string[], requiredSkills: string[]): {
  score: number;
  matchedSkills: string[];
  missingSkills: string[];
} => {
  if (!requiredSkills || requiredSkills.length === 0) {
    return { score: 100, matchedSkills: [], missingSkills: [] };
  }

  const matchedSkills = requiredSkills.filter(skill => 
    employeeSkills.some(empSkill => 
      empSkill.toLowerCase().includes(skill.toLowerCase()) ||
      skill.toLowerCase().includes(empSkill.toLowerCase())
    )
  );

  const missingSkills = requiredSkills.filter(skill => !matchedSkills.includes(skill));
  const score = (matchedSkills.length / requiredSkills.length) * 100;

  return { score, matchedSkills, missingSkills };
};

/**
 * 计算经验评分
 */
const calculateExperienceScore = (employee: any, preferredExperience?: number): number => {
  if (!preferredExperience) return 100;
  
  const hireDate = new Date(employee.hireDate);
  const yearsOfExperience = (Date.now() - hireDate.getTime()) / (365.25 * 24 * 60 * 60 * 1000);
  
  if (yearsOfExperience >= preferredExperience) {
    return 100;
  } else {
    return Math.max(50, (yearsOfExperience / preferredExperience) * 100);
  }
};

/**
 * 计算位置评分
 */
const calculateLocationScore = (employee: any, targetStationId?: string): number => {
  if (!targetStationId) return 100;
  
  // 如果员工已在目标电站，评分较低（避免不必要的调度）
  if (employee.currentStationId?.toString() === targetStationId) {
    return 30;
  }
  
  // 如果是员工的家乡电站，评分较高
  if (employee.homeStationId?.toString() === targetStationId) {
    return 90;
  }
  
  // 其他情况中等评分
  return 70;
};

/**
 * 使用AI增强推荐分析
 */
const enhanceRecommendationWithAI = async (
  employee: any,
  businessStatus: EmployeeBusinessStatus,
  requirement: string,
  recommendationConfig: RecommendationConfig
): Promise<{
  strengthReasons: string[];
  riskFactors: string[];
  learningOpportunities: string[];
  skillDevelopment: string[];
}> => {
  // 先准备后备方案的分析结果
  const strengthReasons: string[] = [];
  const riskFactors: string[] = [];
  const learningOpportunities: string[] = [];
  const skillDevelopment: string[] = [];

  // 分析优势
  if (businessStatus.performanceRating > 85) {
    strengthReasons.push('绩效表现优秀，工作能力强');
  }
  if (businessStatus.fatigueScore < 30) {
    strengthReasons.push('工作状态良好，精力充沛');
  }
  if (employee.skills && employee.skills.length > 3) {
    strengthReasons.push('技能丰富，适应性强');
  }
  if (employee.certifications && employee.certifications.length > 0) {
    strengthReasons.push('持有相关证书，专业能力强');
  }

  // 分析风险
  if (businessStatus.fatigueScore > 60) {
    riskFactors.push('疲劳度较高，需要关注工作状态');
  }
  if (businessStatus.continuousDispatches > 2) {
    riskFactors.push('连续调度次数较多，建议适当休息');
  }
  if (employee.employeeType === 'intern') {
    riskFactors.push('实习员工，需要额外指导和支持');
  }

  // 学习机会
  if (requirement.includes('新技术') || requirement.includes('培训')) {
    learningOpportunities.push('接触新技术和工作方法的机会');
  }
  if (requirement.includes('管理') || requirement.includes('领导')) {
    learningOpportunities.push('提升管理和领导能力的机会');
  }
  if (requirement.includes('维护') || requirement.includes('检修')) {
    learningOpportunities.push('提升设备维护和检修技能');
  }

  // 技能发展
  if (employee.position.includes('技术')) {
    skillDevelopment.push('深化技术专业能力');
  }
  if (employee.position.includes('管理')) {
    skillDevelopment.push('提升团队管理能力');
  }
  skillDevelopment.push('跨部门协作能力提升');

  // 尝试AI增强分析（如果失败则使用上面的基础分析）
  try {
    const systemPrompt = `你是一个专业的人力资源分析师，专门分析员工调度的优势、风险和发展机会。

请分析以下员工信息并提供详细的分析：

员工信息：
- 姓名：${employee.name}
- 职位：${employee.position}
- 部门：${employee.department}
- 技能：${employee.skills?.join(', ') || '无'}
- 证书：${employee.certifications?.join(', ') || '无'}
- 疲劳度：${businessStatus.fatigueScore}/100
- 连续调度次数：${businessStatus.continuousDispatches}
- 绩效评分：${businessStatus.performanceRating}/100

调度需求：${requirement}

请返回JSON格式：
{
  "strengthReasons": ["优势1", "优势2", "优势3"],
  "riskFactors": ["风险1", "风险2"],
  "learningOpportunities": ["学习机会1", "学习机会2"],
  "skillDevelopment": ["技能发展1", "技能发展2"]
}`;

    // 如果有AI配置且启用，尝试调用AI
    if (config.dashscope?.apiKey) {
      const content = await callQwenAPI(systemPrompt, '请分析这位员工的调度适合度', 0.1);
      
      if (content) {
        const jsonMatch = content.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          const result = JSON.parse(jsonMatch[0]);
          // 如果AI返回了有效结果，使用AI的分析
          if (result.strengthReasons && Array.isArray(result.strengthReasons)) {
            return {
              strengthReasons: result.strengthReasons || strengthReasons,
              riskFactors: result.riskFactors || riskFactors,
              learningOpportunities: result.learningOpportunities || learningOpportunities,
              skillDevelopment: result.skillDevelopment || skillDevelopment
            };
          }
        }
      }
    }
  } catch (error) {
    console.warn('AI增强分析失败，使用基础分析:', (error as Error).message || error);
  }

  // 返回基础分析结果
  return { strengthReasons, riskFactors, learningOpportunities, skillDevelopment };
};

/**
 * 增强版AI智能推荐 - 优化版本
 */
export const getEnhancedAIRecommendations = async (
  request: RecommendationRequest
): Promise<RecommendationResponse> => {
  try {
    console.log('🚀 开始增强版AI智能推荐分析（优化版）');
    console.log('📋 请求参数:', {
      requirement: request.requirement,
      targetStationId: request.targetStationId,
      urgencyLevel: request.urgencyLevel,
      maxCandidates: request.maxCandidates
    });

    // 合并配置
    const finalConfig = { ...DEFAULT_RECOMMENDATION_CONFIG, ...request.config };

    // 解析需求中的技能要求和职位要求
    const requiredSkills = request.requiredSkills || extractSkillsFromRequirement(request.requirement);
    const requiredPositions = extractPositionsFromRequirement(request.requirement);
    console.log('🔍 解析的技能要求:', requiredSkills);
    console.log('🔍 解析的职位要求:', requiredPositions);

    // 构建预筛选查询条件
    const preFilterQuery: any = { status: 'active' };
    
    // 根据职位要求预筛选
    if (requiredPositions.length > 0) {
      preFilterQuery.$or = requiredPositions.map(pos => ({
        position: { $regex: pos, $options: 'i' }
      }));
    }

    // 根据技能要求预筛选
    if (requiredSkills.length > 0) {
      if (preFilterQuery.$or) {
        // 如果已有职位筛选，添加技能筛选
        preFilterQuery.$and = [
          { $or: preFilterQuery.$or },
          { 
            $or: requiredSkills.map(skill => ({
              skills: { $regex: skill, $options: 'i' }
            }))
          }
        ];
        delete preFilterQuery.$or;
      } else {
        // 只有技能筛选
        preFilterQuery.$or = requiredSkills.map(skill => ({
          skills: { $regex: skill, $options: 'i' }
        }));
      }
    }

    // 根据目标电站预筛选（优先选择附近的员工）
    let targetStationFilter = {};
    if (request.targetStationId) {
      targetStationFilter = {
        $or: [
          { currentStationId: request.targetStationId },
          { homeStationId: request.targetStationId }
        ]
      };
    }

    console.log('🔍 预筛选查询条件:', JSON.stringify(preFilterQuery, null, 2));

    // 第一步：获取预筛选的候选人（限制数量以提高性能）
    const maxPreFilterCandidates = Math.max(50, (request.maxCandidates || 10) * 5);
    let candidates = await Employee.find(preFilterQuery)
      .populate('homeStationId')
      .populate('currentStationId')
      .limit(maxPreFilterCandidates);

    console.log(`📊 预筛选找到 ${candidates.length} 名候选员工`);

    // 如果预筛选结果太少，扩大搜索范围
    if (candidates.length < 10 && requiredSkills.length > 0) {
      console.log('🔄 预筛选结果较少，扩大搜索范围...');
      const fallbackQuery = { status: 'active' };
      const additionalCandidates = await Employee.find(fallbackQuery)
        .populate('homeStationId')
        .populate('currentStationId')
        .limit(30);
      
      // 合并结果并去重
      const candidateIds = new Set(candidates.map(c => (c as any)._id.toString()));
      const newCandidates = additionalCandidates.filter(c => 
        !candidateIds.has((c as any)._id.toString())
      );
      candidates = [...candidates, ...newCandidates];
      console.log(`📊 扩大搜索后共有 ${candidates.length} 名候选员工`);
    }

    // 如果没有候选人，返回空结果
    if (candidates.length === 0) {
      console.warn('⚠️ 没有找到符合条件的候选员工');
      return {
        recommendations: [],
        summary: {
          totalCandidates: 0,
          averageScore: 0,
          riskLevel: 'critical' as const,
          confidence: 0
        },
        analysis: '没有找到符合条件的候选员工，建议调整需求条件或扩大搜索范围。',
        riskAssessment: '无可用员工，无法进行调度。',
        alternatives: ['调整技能要求', '考虑培训现有员工', '外部招聘'],
        configUsed: finalConfig
      };
    }

    // 第二步：按特征分组，选择代表样本（大幅优化性能）
    console.log('🔄 开始按特征分组选择代表样本...');
    
    // 为每个员工计算特征指纹
    const employeeGroups = new Map<string, Array<{employee: any, score: number, skillMatch: any}>>();
    
    for (const employee of candidates) {
      try {
        // 快速计算基础评分
        const skillMatch = calculateSkillMatch((employee as any).skills || [], requiredSkills);
        const experienceScore = calculateExperienceScore(employee, request.preferredExperience);
        const locationScore = calculateLocationScore(employee, request.targetStationId);
        
        // 简化的综合评分
        const quickScore = (
          skillMatch.score * 0.4 +
          experienceScore * 0.3 +
          locationScore * 0.3
        );

        // 生成特征指纹（用于分组）
        const fingerprint = generateEmployeeFingerprint(employee, skillMatch, experienceScore, locationScore);
        
        if (!employeeGroups.has(fingerprint)) {
          employeeGroups.set(fingerprint, []);
        }
        
        employeeGroups.get(fingerprint)!.push({
          employee,
          score: quickScore,
          skillMatch
        });
      } catch (error) {
        console.warn(`快速评分失败: ${(employee as any).name}`, error);
      }
    }

    console.log(`📊 员工分组结果: ${employeeGroups.size} 个不同特征组`);

    // 从每组选择最高分的代表，最多选择8个代表进行详细分析
    const representatives: Array<{employee: any, score: number, skillMatch: any, groupSize: number}> = [];
    
    for (const [fingerprint, group] of employeeGroups) {
      // 按评分排序，选择最高分作为代表
      group.sort((a, b) => b.score - a.score);
      const representative = group[0];
      
      representatives.push({
        ...representative,
        groupSize: group.length
      });
    }

    // 按代表评分排序，选择前8名进行详细分析
    representatives.sort((a, b) => b.score - a.score);
    const maxDetailedAnalysis = Math.min(8, representatives.length);
    const topCandidates = representatives.slice(0, maxDetailedAnalysis);
    
    console.log(`🎯 选择 ${topCandidates.length} 个代表样本进行详细分析`);
    topCandidates.forEach((rep, index) => {
      console.log(`  ${index + 1}. ${rep.employee.name} (代表${rep.groupSize}人, 评分:${Math.round(rep.score)})`);
    });

    // 第三步：对代表样本进行详细分析，然后扩展到整个组
    const detailedAnalysis: Array<{
      representative: any,
      groupEmployees: any[],
      analysis: any,
      score: number
    }> = [];

    for (const candidateData of topCandidates) {
      try {
        const employee = candidateData.employee;
        console.log(`🔄 详细分析代表员工: ${employee.name} (代表${candidateData.groupSize}人)`);
        
        // 获取员工业务状态
        const businessStatus = await getEmployeeBusinessStatus(employee._id.toString());

        // 如果员工不可用且非紧急情况，跳过整个组
        if (!businessStatus.isAvailable && request.urgencyLevel !== 'emergency') {
          console.log(`⏭️ 跳过不可用员工组: ${employee.name}`);
          continue;
        }

        // 重新计算完整评分（包含业务状态）
        const skillMatch = candidateData.skillMatch;
        const experienceScore = calculateExperienceScore(employee, request.preferredExperience);
        const locationScore = calculateLocationScore(employee, request.targetStationId);
        const availabilityScore = businessStatus.isAvailable ? 100 : Math.max(0, 100 - businessStatus.fatigueScore);

        // 计算综合评分
        const overallScore = (
          skillMatch.score * (finalConfig.skillMatchWeight / 100) +
          experienceScore * (finalConfig.experienceWeight / 100) +
          locationScore * (finalConfig.locationWeight / 100) +
          availabilityScore * 0.1 +
          businessStatus.performanceRating * 0.1
        );

        // 使用AI增强分析（仅对代表进行AI分析）
        let aiAnalysis;
        if (overallScore > 70) {
          aiAnalysis = await enhanceRecommendationWithAI(
            employee, 
            businessStatus, 
            request.requirement, 
            finalConfig
          );
        } else {
          // 对低分候选人使用简化分析
          aiAnalysis = {
            strengthReasons: ['基础技能匹配'],
            riskFactors: ['评分较低，需要进一步评估'],
            learningOpportunities: ['提升相关技能'],
            skillDevelopment: ['加强专业能力培训']
          };
        }

        // 找到该代表所在的组
        const fingerprint = generateEmployeeFingerprint(employee, skillMatch, experienceScore, locationScore);
        const groupEmployees = employeeGroups.get(fingerprint) || [];

        detailedAnalysis.push({
          representative: {
            employee,
            businessStatus,
            skillMatch,
            experienceScore,
            locationScore,
            availabilityScore,
            overallScore,
            aiAnalysis
          },
          groupEmployees: groupEmployees.map(g => g.employee),
          analysis: aiAnalysis,
          score: overallScore
        });

        console.log(`📊 代表员工 ${employee.name} 详细评分: ${Math.round(overallScore)}`);
      } catch (error) {
        console.error(`❌ 详细分析代表员工失败:`, error);
      }
    }

    // 按代表评分排序
    detailedAnalysis.sort((a, b) => b.score - a.score);

    console.log(`✅ 成功分析 ${detailedAnalysis.length} 个代表样本`);

    // 第四步：从高分组中选择最终推荐员工
    const recommendations: EnhancedRecommendation[] = [];
    const maxCandidates = request.maxCandidates || 5; // 默认推荐5人
    
    for (const group of detailedAnalysis) {
      if (recommendations.length >= maxCandidates) break;
      
      // 从该组中选择最多2名员工（如果组内人数多的话）
      const groupLimit = Math.min(2, maxCandidates - recommendations.length);
      const selectedFromGroup = group.groupEmployees
        .slice(0, groupLimit)
        .map(employee => {
          // 使用代表的分析结果应用到组内其他员工
          const rep = group.representative;
          
          // 计算风险评分
          const riskScore = Math.max(
            rep.businessStatus.fatigueScore,
            rep.businessStatus.continuousDispatches * 20,
            rep.skillMatch.missingSkills.length * 15
          );

          // 计算建议调度天数
          const recommendedDuration = calculateRecommendedDuration(
            request.duration,
            rep.businessStatus,
            request.urgencyLevel
          );

          const recommendation: EnhancedRecommendation = {
            employeeId: employee._id.toString(),
            employee: {
              _id: employee._id,
              name: employee.name,
              position: employee.position,
              department: employee.department,
              employeeType: employee.employeeType,
              status: employee.status,
              skills: employee.skills,
              certifications: employee.certifications,
              currentStation: employee.currentStationId,
              homeStation: employee.homeStationId
            },
            businessStatus: rep.businessStatus,
            overallScore: Math.round(rep.overallScore),
            skillMatchScore: Math.round(rep.skillMatch.score),
            experienceScore: Math.round(rep.experienceScore),
            availabilityScore: Math.round(rep.availabilityScore),
            riskScore: Math.round(riskScore),
            matchedSkills: rep.skillMatch.matchedSkills,
            missingSkills: rep.skillMatch.missingSkills,
            strengthReasons: rep.aiAnalysis.strengthReasons,
            riskFactors: rep.aiAnalysis.riskFactors,
            recommendedDuration,
            suggestedStartDate: calculateSuggestedStartDate(rep.businessStatus, request.startDate),
            alternativeOptions: generateAlternativeOptions(employee, rep.businessStatus),
            learningOpportunities: rep.aiAnalysis.learningOpportunities,
            skillDevelopment: rep.aiAnalysis.skillDevelopment
          };

          return recommendation;
        });
      
      recommendations.push(...selectedFromGroup);
    }

    console.log(`✅ 成功分析 ${recommendations.length} 名员工`);

    // 排序推荐结果
    recommendations.sort((a, b) => b.overallScore - a.overallScore);

    // 限制返回数量
    const finalMaxCandidates = request.maxCandidates || 10;
    const finalRecommendations = recommendations.slice(0, finalMaxCandidates);

    // 计算汇总信息
    const summary = {
      totalCandidates: finalRecommendations.length,
      averageScore: finalRecommendations.length > 0 
        ? Math.round(finalRecommendations.reduce((sum, r) => sum + r.overallScore, 0) / finalRecommendations.length)
        : 0,
      riskLevel: calculateOverallRiskLevel(finalRecommendations),
      confidence: calculateConfidence(finalRecommendations, request)
    };

    console.log('📊 推荐结果汇总:', summary);

    return {
      recommendations: finalRecommendations,
      summary,
      analysis: generateAnalysisText(finalRecommendations, request),
      riskAssessment: generateRiskAssessment(finalRecommendations, finalConfig),
      alternatives: generateAlternatives(finalRecommendations),
      configUsed: finalConfig
    };

  } catch (error) {
    console.error('❌ 增强版AI推荐失败:', error);
    throw new Error(`AI推荐服务失败: ${(error as Error).message || '未知错误'}`);
  }
};

// 辅助函数
const extractSkillsFromRequirement = (requirement: string): string[] => {
  const skillKeywords = ['电工', '电气', '维护', '安全', '管理', '技术', '运维', '检修', 'DCS', '热控'];
  return skillKeywords.filter(skill => requirement.includes(skill));
};

const extractPositionsFromRequirement = (requirement: string): string[] => {
  const positionKeywords = ['值长', 'DCS专工', '电工', '热控', '运维', '检修', '技术员', '工程师', '主任', '班长', '专工'];
  return positionKeywords.filter(position => requirement.includes(position));
};

const generateEmployeeFingerprint = (employee: any, skillMatch: any, experienceScore: number, locationScore: number): string => {
  // 生成员工特征指纹，相似特征的员工会被分到同一组
  const features = [
    // 职位特征（主要分组依据）
    employee.position || 'unknown',
    // 技能匹配度等级（按20分为一档）
    Math.floor(skillMatch.score / 20) * 20,
    // 经验等级（按20分为一档）
    Math.floor(experienceScore / 20) * 20,
    // 位置评分等级（按30分为一档）
    Math.floor(locationScore / 30) * 30,
    // 所属电站
    employee.homeStationId?.name || 'unknown',
    // 当前状态
    employee.status || 'unknown'
  ];
  
  return features.join('|');
};

const calculateRecommendedDuration = (
  requestedDuration?: number,
  businessStatus?: EmployeeBusinessStatus,
  urgencyLevel?: string
): number => {
  if (requestedDuration) return requestedDuration;
  
  if (urgencyLevel === 'emergency') return 3;
  if (urgencyLevel === 'high') return 7;
  if (businessStatus && businessStatus.fatigueScore > 60) return 5; // 疲劳员工建议短期调度
  
  return 14; // 默认两周
};

const calculateSuggestedStartDate = (
  businessStatus: EmployeeBusinessStatus,
  requestedStartDate?: Date
): Date => {
  if (requestedStartDate) return requestedStartDate;
  
  const now = new Date();
  if (businessStatus.fatigueScore > 60) {
    // 疲劳员工建议延后开始
    now.setDate(now.getDate() + 2);
  }
  
  return now;
};

const generateAlternativeOptions = (employee: any, businessStatus: EmployeeBusinessStatus): string[] => {
  const options = [];
  
  if (businessStatus.fatigueScore > 60) {
    options.push('建议安排短期调度或延后执行');
  }
  
  if (businessStatus.continuousDispatches > 2) {
    options.push('考虑安排其他员工或分阶段执行');
  }
  
  if (employee.employeeType === 'intern') {
    options.push('建议配备经验丰富的员工指导');
  }
  
  return options;
};

const calculateOverallRiskLevel = (recommendations: EnhancedRecommendation[]): 'low' | 'medium' | 'high' | 'critical' => {
  if (recommendations.length === 0) return 'critical';
  
  const avgRiskScore = recommendations.reduce((sum, r) => sum + r.riskScore, 0) / recommendations.length;
  
  if (avgRiskScore < 30) return 'low';
  if (avgRiskScore < 60) return 'medium';
  if (avgRiskScore < 80) return 'high';
  return 'critical';
};

const calculateConfidence = (recommendations: EnhancedRecommendation[], request: RecommendationRequest): number => {
  if (recommendations.length === 0) return 0;
  
  const avgScore = recommendations.reduce((sum, r) => sum + r.overallScore, 0) / recommendations.length;
  const hasHighScoreCandidate = recommendations.some(r => r.overallScore > 80);
  
  let confidence = avgScore;
  if (hasHighScoreCandidate) confidence += 10;
  if (request.urgencyLevel === 'low') confidence += 5;
  
  return Math.min(100, Math.round(confidence));
};

const generateAnalysisText = (recommendations: EnhancedRecommendation[], request: RecommendationRequest): string => {
  if (recommendations.length === 0) {
    return '未找到合适的候选人，建议调整需求条件或考虑外部招聘。';
  }
  
  const topCandidate = recommendations[0];
  return `基于AI分析，共找到${recommendations.length}名候选人。推荐${topCandidate.employee.name}（${topCandidate.employee.position}），综合评分${topCandidate.overallScore}分，具有${topCandidate.matchedSkills.join('、')}等相关技能。`;
};

const generateRiskAssessment = (recommendations: EnhancedRecommendation[], config: RecommendationConfig): string => {
  const highRiskCandidates = recommendations.filter(r => r.riskScore > 60).length;
  const fatigueRiskCandidates = recommendations.filter(r => r.businessStatus.fatigueScore > config.fatigueThreshold).length;
  
  if (highRiskCandidates === 0) {
    return '整体风险较低，推荐的候选人都具有良好的可用性和适合度。';
  }
  
  return `${highRiskCandidates}名候选人存在较高风险，其中${fatigueRiskCandidates}名员工疲劳度较高，建议谨慎选择或安排适当休息。`;
};

const generateAlternatives = (recommendations: EnhancedRecommendation[]): string[] => {
  const alternatives = [];
  
  if (recommendations.length > 3) {
    alternatives.push('考虑分批调度，降低单次调度压力');
  }
  
  if (recommendations.some(r => r.employee.employeeType === 'intern')) {
    alternatives.push('安排实习生参与，提供学习机会');
  }
  
  if (recommendations.some(r => r.businessStatus.fatigueScore > 60)) {
    alternatives.push('优先选择状态良好的员工，避免过度疲劳');
  }
  
  return alternatives;
};