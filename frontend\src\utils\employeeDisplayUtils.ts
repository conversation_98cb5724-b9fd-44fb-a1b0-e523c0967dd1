import { Employee, EmploymentStatus, EmployeeType } from '../types';

// 员工显示格式化工具函数
export const formatEmployeeDisplay = (employee: Employee) => {
  // 处理特殊状态的显示
  if (employee.name === '——') {
    return {
      displayName: '——',
      statusText: '',
      backgroundColor: 'bg-gray-600/30',
      borderColor: 'border-gray-500/50',
      textColor: 'text-gray-400'
    };
  }

  // 处理空缺岗位
  if (employee.status === 'vacant' || employee.name === '空缺') {
    return {
      displayName: '(空缺)',
      statusText: '',
      backgroundColor: 'bg-red-600/30',
      borderColor: 'border-red-400/70',
      textColor: 'text-red-300'
    };
  }

  // 处理招聘中
  if (employee.status === 'pending' && (employee.name === '招聘中' || employee.name.includes('招聘'))) {
    return {
      displayName: '(招聘中)',
      statusText: '',
      backgroundColor: 'bg-orange-600/30',
      borderColor: 'border-orange-400/70',
      textColor: 'text-orange-300'
    };
  }

  // 处理外委人员
  if (employee.status === 'external_contract' || employee.employeeType === 'external') {
    const companyName = (employee as any).externalCompany || '外委';
    return {
      displayName: companyName === '外委' ? '(外委)' : companyName,
      statusText: '(外委)',
      backgroundColor: 'bg-purple-600/30',
      borderColor: 'border-purple-400/70',
      textColor: 'text-purple-300'
    };
  }

  // 处理正常员工
  let displayName = employee.name;
  let statusText = '';
  let backgroundColor = 'bg-slate-800/80';
  let borderColor = 'border-slate-700/50';
  let textColor = 'text-white';

  // 根据状态添加后缀
  if (employee.status === 'inactive') {
    statusText = '(待入职)';
    backgroundColor = 'bg-yellow-600/30';
    borderColor = 'border-yellow-400/70';
    textColor = 'text-yellow-300';
  } else if (employee.status === 'learning') {
    statusText = '(学习中)';
    backgroundColor = 'bg-blue-600/30';
    borderColor = 'border-blue-400/70';
    textColor = 'text-blue-300';
  } else if ((employee as any).borrowedFromStation || (employee as any).borrowedToStation) {
    statusText = '(借调中)';
    backgroundColor = 'bg-indigo-600/30';
    borderColor = 'border-indigo-400/70';
    textColor = 'text-indigo-300';
  } else if ((employee as any).isConcurrent) {
    statusText = '(兼)';
    backgroundColor = 'bg-green-600/30';
    borderColor = 'border-green-400/70';
    textColor = 'text-green-300';
  }

  return {
    displayName,
    statusText,
    backgroundColor,
    borderColor,
    textColor
  };
};

// 获取员工状态标签
export const getEmployeeStatusBadges = (employee: Employee) => {
  const badges = [];

  // 调度状态
  if ((employee as any).isDispatched) {
    const homeStationName = (employee as any).homeStationName;
    const currentStationName = (employee as any).currentStationName;
    const dispatchFromStation = (employee as any).dispatchFromStation;
    const dispatchToStation = (employee as any).dispatchToStation;
    
    // 判断调入还是调出：
    // 关键是要从当前查看页面的视角来判断
    // 如果员工的归属电站和当前电站不同，需要判断员工是被调入还是调出
    
    if (homeStationName && currentStationName && homeStationName !== currentStationName) {
      // 如果员工的归属电站不是当前电站，说明员工是从其他电站调入的
      if (homeStationName !== currentStationName) {
        badges.push({
          text: `📥 从${homeStationName}调入`,
          className: 'bg-green-600/40 text-green-200'
        });
      }
    }
    
    // 如果有明确的调度记录信息
    if (dispatchFromStation && dispatchToStation && dispatchFromStation !== dispatchToStation) {
      // 从调度记录的角度判断：如果当前电站是调入目标，显示调入；如果是调出源，显示调出
      badges.push({
        text: `🔄 从${dispatchFromStation}调至${dispatchToStation}`,
        className: 'bg-blue-600/40 text-blue-200'
      });
    }
  }

  // 员工状态
  if (employee.status === 'pending') {
    badges.push({
      text: '待入职',
      className: 'bg-orange-600/40 text-orange-200'
    });
  } else if (employee.status === 'learning') {
    badges.push({
      text: '📚 学习',
      className: 'bg-purple-500/30 text-purple-300'
    });
  } else if (employee.status === 'vacant') {
    badges.push({
      text: '❌ 空缺',
      className: 'bg-red-500/30 text-red-300'
    });
  }

  // 员工类型
  if (employee.employeeType === EmployeeType.INTERN) {
    badges.push({
      text: '🎓 实习',
      className: 'bg-blue-500/30 text-blue-300'
    });
  } else if (employee.employeeType === EmployeeType.EXTERNAL) {
    badges.push({
      text: '🏢 外委',
      className: 'bg-gray-500/30 text-gray-400'
    });
  } else if (employee.employeeType === 'borrowed') {
    badges.push({
      text: '🔄 借调',
      className: 'bg-indigo-500/30 text-indigo-300'
    });
  }

  // 兼职标识
  if ((employee as any).isConcurrent) {
    badges.push({
      text: '兼',
      className: 'bg-green-500/30 text-green-300'
    });
  }

  return badges;
};