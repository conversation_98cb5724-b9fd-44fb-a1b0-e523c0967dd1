import React, { useState } from 'react';
import { DispatchRecord } from '../services/dispatchService';
import { Employee } from '../types';
import { useDispatchData } from '../hooks/useDispatchData';

// 导入拆分的组件
import DispatchTabs from '../components/dispatch/DispatchTabs';
import OverviewTab from '../components/dispatch/OverviewTab';
import PlanningTab from '../components/dispatch/PlanningTab';
import ApprovalTab from '../components/dispatch/ApprovalTab';
import ApprovalModal from '../components/dispatch/ApprovalModal';
import RiskAnalysisModal from '../components/dispatch/RiskAnalysisModal';
import RecommendationConfigModal from '../components/RecommendationConfigModal';

const DispatchCenterPage: React.FC = () => {
  // 使用自定义Hook管理数据
  const { 
    dispatchRecords, 
    employees, 
    stations, 
    loading, 
    dataLoaded,
    fetchDispatchRecords 
  } = useDispatchData();

  // 页面状态
  const [activeTab, setActiveTab] = useState<'overview' | 'planning' | 'approval'>('overview');
  
  // 模态框状态
  const [showApprovalModal, setShowApprovalModal] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState<DispatchRecord | null>(null);
  const [showRecommendationConfig, setShowRecommendationConfig] = useState(false);
  const [showRiskAnalysis, setShowRiskAnalysis] = useState(false);

  // 事件处理函数
  const handleApprove = (record: DispatchRecord) => {
    setSelectedRecord(record);
    setShowApprovalModal(true);
  };

  const handleApprovalSubmit = async (recordId: string, approved: boolean, comment: string) => {
    try {
      console.log('🔄 审核调度申请:', { recordId, approved, comment });
      // 这里调用审核API
      // await dispatchService.approveDispatch(recordId, { approved, comment });
      console.log('✅ 审核成功');
      fetchDispatchRecords();
    } catch (error) {
      console.error('❌ 审核失败:', error);
    }
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview':
        return (
          <OverviewTab 
            employees={employees}
            stations={stations}
            dispatchRecords={dispatchRecords}
          />
        );
      case 'planning':
        return <PlanningTab />;
      case 'approval':
        return (
          <ApprovalTab 
            dispatchRecords={dispatchRecords}
            onApprove={handleApprove}
          />
        );

      default:
        return null;
    }
  };

  if (loading && !dataLoaded) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">正在加载调度中心数据...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* 页面标题 */}
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">调度中心</h1>
        <div className="text-sm text-gray-500">
          数据更新时间: {new Date().toLocaleString()}
        </div>
      </div>

      {/* 标签页导航 */}
      <DispatchTabs 
        activeTab={activeTab}
        onTabChange={setActiveTab}
      />

      {/* 标签页内容 */}
      <div className="min-h-[600px]">
        {renderTabContent()}
      </div>

      {/* 模态框 */}
      <ApprovalModal
        isOpen={showApprovalModal}
        record={selectedRecord}
        onClose={() => {
          setShowApprovalModal(false);
          setSelectedRecord(null);
        }}
        onApprove={handleApprovalSubmit}
      />

      <RiskAnalysisModal
        isOpen={showRiskAnalysis}
        onClose={() => setShowRiskAnalysis(false)}
      />

      <RecommendationConfigModal
        isOpen={showRecommendationConfig}
        onClose={() => setShowRecommendationConfig(false)}
        onSave={(config) => {
          console.log('保存推荐配置:', config);
          setShowRecommendationConfig(false);
        }}
      />
    </div>
  );
};

export default DispatchCenterPage;