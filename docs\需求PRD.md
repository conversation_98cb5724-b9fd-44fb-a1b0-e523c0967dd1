光热电站智慧运维管理平台
产品需求与技术概要设计文档 (PRD & TDD)
版本: 1.0
日期: 2025-07-22
1. 项目概述
1.1. 项目背景与目标
随着新能源电站规模的扩大和运营复杂性的增加，传统的人工管理和调度模式已难以满足高效、安全、低成本的运营需求。本项目旨在打造一个集“数据监控、智能调度、精细化运维”于一体的智慧运维管理平台。
核心目标:
全局可视: 通过数据大屏实现对所有电站核心运营指标的实时监控与态势感知。
调度智能: 利用AI辅助，将传统的人员调度从“经验驱动”转变为“数据驱动”，实现人员资源的最佳配置，降低成本，提高响应速度。
运维高效: 将人员、工单、巡检、耗材等运维流程线上化、标准化，提升整体运维效率与管理水平。
1.2. 目标用户
集团/区域管理者: 关注宏观运营数据、发电效率、成本控制。
电站站长/经理: 关注本站的实时运行状态、人员配置、任务执行情况。
调度中心操作员: 负责制定、执行和跟踪人员调度计划。
运维工程师/班长: 接收和执行工单、巡检任务，管理团队。
2. 功能模块详细需求 (PRD)
2.1. 模块一：监控数据大屏 (Monitoring Dashboard)
用户故事: 作为管理者，我希望能在一个屏幕上看到所有电站的宏观运营情况，并能快速定位异常，以便做出决策。
功能需求分析 (基于截图):
2.1.1. 核心指标卡片 (KPI Cards)
总装机容量: 所有电站容量总和。
实时总负荷: 所有电站当前发电功率总和 (MW)。
当日总发电量: 所有电站从当日零点至今的累计发电量 (MWh)。
实时告警: 顶部横幅或独立卡片，显示当前最高级别的告警信息，可点击查看列表。
2.1.2. 电站分布与状态
在地图上标记所有电站的地理位置。
鼠标悬浮或点击电站图标时，显示该站的核心信息（名称、实时负荷、当日发电量、状态）。
电站图标应根据运行状态（正常、告警、离线）显示不同颜色。
2.1.3. 系统效率监控
整体效率: 以仪表盘或水球图形式，展示所有电站的平均系统效率。
分站效率排行: 列表或条形图展示各电站的效率，并用颜色区分优良中差等级。
2.1.4. 多发电站趋势分析
发电负荷曲线: 在同一坐标系下，用不同颜色的曲线绘制多个核心电站的24小时实时发电负荷，用于直观对比发电趋势。
发电量对比: 柱状图或条形图，对比不同电站在选定时间（日/月/年）内的总发电量。支持“同期/环期”对比。
2.1.5. 单站深度分析 (可下钻)
实时放电/充电量曲线: 展示单个电站的储能系统实时充放电功率。
发电功率趋势: 对比“实际发电功率”、“目标功率”、“预测功率”三条曲线，分析发电达成情况。
2.2. 模块二：智能调度中心 (Dispatch Center)
用户故事: 作为调度员，我需要快速找到最合适的人去支援一个紧急任务，并希望系统能给我智能推荐。作为管理者，我需要审批调度计划并查看历史记录。
2.2.1. 调度计划管理
创建计划: 提供表单，可选择被调度员工、原电站、目标电站、目标岗位、调度类型（紧急支援/计划性轮岗）、起止时间、原因等。
审批流程: 调度计划提交后进入审批流程（如：待审批 -> 调度中心主管审批 -> 人力资源审批 -> 已批准/已驳回）。审批人可在线查看计划详情并操作。
计划看板/列表: 以看板或列表形式展示所有调度计划，按状态（待审批、进行中、已完成）分类。
2.2.2. AI辅助调度决策 (核心功能)
智能推荐入口: 在创建调度计划时，当选择“目标岗位”后，系统提供“智能推荐”按钮。
需求输入: 管理员可通过两种方式输入需求：
结构化表单 (主): 清晰地选择“目标电站”、“岗位”、“必需技能”（如高压焊工证）、“调度时长”等。
自然语言辅助 (辅): 提供一个输入框，允许输入“我需要一个有高压焊工证的机务去金塔支援3个月”。AI（LLM）负责解析这段话并自动填充到结构化表单中，然后由用户确认。这解决了响应慢和结果不准的问题，AI只做它最擅长的语义理解，而非复杂的业务逻辑查询。
智能推荐引擎 (后端):
候选池筛选: 根据岗位和必需技能，从Staffing和Employee表中筛选出所有符合基本条件的员工。
多维度评分模型: 对每个候选人进行量化打分，分数越高越推荐。评分维度包括：
技能匹配度 (权重: 40%): 拥有该岗位所需技能的数量和熟练度。
人员可用性 (权重: 30%): 员工当前是否在执行关键任务，或是否刚结束一次长期调度（避免疲劳）。
成本效益 (权重: 20%): 优先推荐距离目标电站更近、职级更匹配的员工，以降低差旅和人力成本。
历史表现 (权重: 10%): 员工过往调度任务的完成情况和评价。
结果呈现: 以列表形式返回TOP 3-5名候选人，清晰展示每个人的综合得分、各维度得分及推荐理由（如：“技能完全匹配，且距离最近”）。管理员可一键选择最优人选填入调度计划。
2.2.3. 调度分析查询
提供多维度（按员工、按电站、按时间）的调度分析查询和数据导出。
2.3. 模块三：运维管理 (Operations Management)
用户故事: 作为电站站长，我需要管理我的人员、分配工单、安排巡检，并确保备品备件充足。
2.3.1. 人员信息管理
基于我们最终确定的数据库模型，提供员工档案管理。
可查看员工基本信息、主岗、兼职岗位、技能证书、调度历史、工单历史。
2.3.2. 工单管理系统
创建工单: 包含工单类型（维修/保养）、标题、描述、关联设备、所属电站、优先级、指派人/班组。
工单流转: 状态（待处理 -> 处理中 -> 待验收 -> 已关闭）。处理人可填写工作日志、关联耗材使用情况。
2.3.3. 巡检管理
巡检模板: 创建标准化的巡检任务模板（设备A检查项1、2、3...）。
巡检计划: 基于模板创建日/周/月度巡检计划，并指派给人员。
移动执行: 运维人员可通过移动端（或平板）接收巡检任务，逐项确认并记录异常、拍照上传。
2.3.4. 耗材仓库管理
耗材清单: 管理所有备品备件信息（名称、型号、库存数量、存放位置）。
出入库管理: 记录耗材的领用和采购入库。工单可直接关联耗材领用。
库存预警: 设置最低安全库存，当低于阈值时系统自动提醒仓库管理员。
3. 数据库设计 (Database Design)
以下仅仅只是基于种子数据文件给的相关表设计，其他需要的数据和字段待补充。
PowerStations: (_id, name, location, type, capacity, status, ...)
PositionTemplates: (_id, positionName, department, description, ...)
Employees: (_id, name, employeeType, contactInfo, hireDate, skills, certifications, ...)
Staffing (核心): (_id, stationId, positionTemplateId, employeeId, status, notes, instance, ...)
stationId -> PowerStations._id
positionTemplateId -> PositionTemplates._id
employeeId -> Employees._id (可为 null)
DispatchRecords: (_id, employeeId, fromStationId, toStationId, position, startDate, endDate, status, ...)
ConcurrentPositions: (_id, employeeId, staffingId, notes, ...)
employeeId -> Employees._id
staffingId -> Staffing._id (指向员工兼任的那个岗位实例)
HiringPlans: (_id, staffingId, source, status, priority, expectedArrivalDate, outsourcingCompany, ...)
staffingId -> Staffing._id (指向需要招聘的那个空缺岗位实例)
WorkOrders: (_id, title, description, stationId, assignedTo_EmployeeId, status, ...)
InspectionTemplates & InspectionRecords: 标准主从表结构。
WarehouseItems & WarehouseLogs: 标准库存及日志表结构。
4. 技术概要设计 (TDD)
4.1. 技术栈推荐
前端: Vue 3 + Vite + TypeScript + Pinia (状态管理) + ECharts (图表库) + Element Plus (UI组件库)。
后端: Node.js + NestJS (企业级框架，内置依赖注入、模块化) + TypeScript。
数据库: MongoDB (与 Mongoose 配合，非常适合我们设计的灵活文档模型)。
AI (LLM) 集成: 调用成熟的云服务API（如 阿里云通义千问 / 智谱AI），仅用于自然语言到结构化数据的转换。
实时通信: WebSocket 或 Socket.IO，用于大屏数据的实时推送。
4.2. 系统架构
采用标准的前后端分离架构。
数据源: 各电站的DCS/SIS系统通过数据采集网关，将实时数据推送至消息队列（如 RabbitMQ）。
后端服务:
数据消费服务: 订阅消息队列，将实时数据处理后存入时序数据库（如 InfluxDB）或直接通过 WebSocket 推送给前端。
API网关: 统一的业务API入口，处理来自前端的HTTP请求。
业务逻辑层: 实现调度、工单、运维等核心业务逻辑。
AI服务层: 封装对外部LLM的调用和内部推荐算法的逻辑。
前端应用: 单页面应用(SPA)，负责数据展示和用户交互。
数据库集群: MongoDB负责业务数据，时序数据库（可选，但推荐）负责存储海量监控数据。