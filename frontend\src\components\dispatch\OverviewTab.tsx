import React, { useState, useEffect } from 'react';
import { Table, Select, Card, Button, Progress, message } from 'antd';
import { Employee } from '../../types';
import DispatchModal from './DispatchModal';
import { apiClient } from '../../services/api';

const { Option } = Select;

interface OverviewTabProps {
  employees: Employee[];
  stations: any[];
  dispatchRecords: any[];
}

interface StationPositionStats {
  total: number;
  occupied: number;
  vacant: number;
  notConfigured: number;
  occupancyRate: string;
}

const OverviewTab: React.FC<OverviewTabProps> = ({
  employees,
  stations,
  dispatchRecords
}) => {
  // 状态管理 - 设置默认值
  const [selectedPosition, setSelectedPosition] = useState<string>('all');
  const [selectedStation, setSelectedStation] = useState<string>('all');
  const [selectedTimeRange, setSelectedTimeRange] = useState<string>('all');
  const [filteredEmployees, setFilteredEmployees] = useState<Employee[]>(employees);
  const [selectedDispatchEmployee, setSelectedDispatchEmployee] = useState<Employee | null>(null);
  const [isDispatchModalOpen, setIsDispatchModalOpen] = useState(false);
  const [loading, setLoading] = useState(false);

  // 数据状态
  const [positions, setPositions] = useState<any[]>([]);
  const [stationList, setStationList] = useState<any[]>([]);
  const [stationStats, setStationStats] = useState<Record<string, StationPositionStats>>({});

  // 获取岗位数据
  const fetchPositions = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/positions/templates', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (response.ok) {
        const result = await response.json();
        setPositions(result.data || []);
      }
    } catch (error) {
      console.error('获取岗位数据失败:', error);
    }
  };

  // 获取电站数据
  const fetchStations = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/stations', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (response.ok) {
        const result = await response.json();
        setStationList(result.data || []);
      }
    } catch (error) {
      console.error('获取电站数据失败:', error);
    }
  };

  // 获取电站岗位统计
  const fetchStationStats = async () => {
    try {
      const statsPromises = (stationList.length > 0 ? stationList : stations).map(station => 
        apiClient.getStationPositionStats(station.id || station._id)
          .then(stats => ({ stationId: station.id || station._id, stats }))
          .catch(error => {
            console.error(`获取电站 ${station.name} 岗位统计失败:`, error);
            return { 
              stationId: station.id || station._id, 
              stats: { total: 0, occupied: 0, vacant: 0, notConfigured: 0, occupancyRate: '0' } 
            };
          })
      );

      const statsResults = await Promise.all(statsPromises);
      const statsMap = statsResults.reduce((acc, { stationId, stats }) => {
        acc[stationId] = stats;
        return acc;
      }, {} as Record<string, StationPositionStats>);

      setStationStats(statsMap);
    } catch (error) {
      console.error('获取电站岗位统计失败:', error);
    }
  };

  // 组件加载时获取数据
  useEffect(() => {
    fetchPositions();
    fetchStations();
  }, []);

  // 当电站数据加载完成后获取统计信息
  useEffect(() => {
    if ((stationList.length > 0 || stations.length > 0)) {
      fetchStationStats();
    }
  }, [stationList, stations]);

  // 获取岗位选项（优先使用后端数据，否则从员工数据中提取）
  const positionOptions = positions.length > 0 
    ? positions.map(pos => pos.positionName).filter(Boolean)
    : Array.from(new Set(employees.map(emp => emp.position).filter(Boolean)));

  // 调度时间选项
  const timeRangeOptions = [
    { value: 'all', label: '调度时间不限' },
    { value: 'half_year', label: '半年以上未调度' },
    { value: 'one_year', label: '一年以上未调度' }
  ];

  // 筛选逻辑
  useEffect(() => {
    let filtered = [...employees];

    // 按岗位筛选
    if (selectedPosition && selectedPosition !== 'all') {
      filtered = filtered.filter(emp => emp.position === selectedPosition);
    }

    // 按电站筛选
    if (selectedStation && selectedStation !== 'all') {
      filtered = filtered.filter(emp => {
        const currentStationId = typeof emp.currentStationId === 'object'
          ? emp.currentStationId?._id || emp.currentStationId?.id
          : emp.currentStationId;
        const homeStationId = typeof emp.homeStationId === 'object'
          ? emp.homeStationId?._id || emp.homeStationId?.id
          : emp.homeStationId;

        return currentStationId === selectedStation || homeStationId === selectedStation;
      });
    }

    // 按调度时间筛选
    if (selectedTimeRange && selectedTimeRange !== 'all') {
      const now = new Date();
      filtered = filtered.filter(emp => {
        if (!emp.lastScheduled) return true; // 没有调度记录的员工

        const lastScheduledDate = new Date(emp.lastScheduled);
        const monthsDiff = (now.getTime() - lastScheduledDate.getTime()) / (1000 * 60 * 60 * 24 * 30);

        if (selectedTimeRange === 'half_year') {
          return monthsDiff >= 6;
        } else if (selectedTimeRange === 'one_year') {
          return monthsDiff >= 12;
        }
        return true;
      });
    }

    setFilteredEmployees(filtered);
  }, [employees, selectedPosition, selectedStation, selectedTimeRange]);

  // 表格列定义
  const columns = [
    {
      title: '姓名',
      dataIndex: 'name',
      key: 'name',
      width: 100,
    },
    {
      title: '岗位',
      dataIndex: 'position',
      key: 'position',
      width: 120,
    },
    {
      title: '经验',
      dataIndex: 'experience',
      key: 'experience',
      width: 80,
      render: (experience: number) => `${experience || 0}年`
    },
    {
      title: '累计调度时长',
      dataIndex: 'totalScheduledDuration',
      key: 'totalScheduledDuration',
      width: 120,
      render: (duration: number) => `${duration || 0}天`
    },
    {
      title: '上次调度时间',
      dataIndex: 'lastScheduled',
      key: 'lastScheduled',
      width: 120,
      render: (date: string) => date ? new Date(date).toLocaleDateString() : '无记录'
    },
    {
      title: '操作',
      key: 'action',
      width: 80,
      render: (_, record) => (
        <Button 
          type="link" 
          size="small"
          className="text-cyan-400 hover:text-cyan-300"
          onClick={() => {
            setSelectedDispatchEmployee(record);
            setIsDispatchModalOpen(true);
          }}
        >
          调度
        </Button>
      ),
    },
  ];



  // 员工负荷数据（Top 5）- 基于真实数据计算
  const topLoadedEmployees = employees
    .sort((a, b) => (b.totalScheduledDuration || 0) - (a.totalScheduledDuration || 0))
    .slice(0, 5)
    .map(emp => ({
      name: emp.name,
      value: emp.totalScheduledDuration || 0
    }));

  return (
    <div className="h-full flex bg-slate-900">
      {/* 左侧：员工列表 */}
      <div className="flex-1 flex flex-col">
        {/* 查询条件 */}
        <div className="p-4 pb-2">
          <Card
            title={<span className="text-cyan-400">个性化查询</span>}
            className="bg-slate-800 border-gray-600"
            styles={{ body: { padding: '16px' } }}
          >
            <div className="flex space-x-4">
              <div className="flex-1">
                <Select
                  value={selectedPosition}
                  onChange={setSelectedPosition}
                  className="w-full"
                  loading={loading}
                >
                  <Option value="all">所有岗位</Option>
                  {positionOptions.map(position => (
                    <Option key={position} value={position}>{position}</Option>
                  ))}
                </Select>
              </div>

              <div className="flex-1">
                <Select
                  value={selectedStation}
                  onChange={setSelectedStation}
                  className="w-full"
                >
                  <Option value="all">所有电站</Option>
                  {(stationList.length > 0 ? stationList : stations).map(station => (
                    <Option key={station.id || station._id} value={station.id || station._id}>
                      {station.name}
                    </Option>
                  ))}
                </Select>
              </div>

              <div className="flex-1">
                <Select
                  value={selectedTimeRange}
                  onChange={setSelectedTimeRange}
                  className="w-full"
                >
                  {timeRangeOptions.map(option => (
                    <Option key={option.value} value={option.value}>{option.label}</Option>
                  ))}
                </Select>
              </div>
            </div>
          </Card>
        </div>

        {/* 员工列表表格 */}
        <div className="flex-1 px-4 pb-4">
          <div className="h-full overflow-auto">
            <Table
              columns={columns}
              dataSource={filteredEmployees}
              rowKey="id"
              pagination={false}
              className="dark-table"
              scroll={{ y: 'calc(100vh - 300px)' }}
            />
          </div>
        </div>
      </div>

      {/* 右侧：统计信息 */}
      <div className="w-80 border-l border-gray-700 flex flex-col">
        {/* AI智能调度按钮 */}
        <div className="p-4 pb-2">
          <Button
            type="primary"
            className="w-full bg-blue-600 hover:bg-blue-700 border-blue-600"
            icon={<span className="mr-1">⚡</span>}
            size="large"
          >
            AI智能调度
          </Button>
        </div>

        {/* 电站岗位配置 */}
        <Card
          title="电站岗位配置"
          className="bg-slate-800 border-gray-600 mx-4 mb-2"
          styles={{ body: { padding: '16px', maxHeight: '400px', overflow: 'auto' } }}
        >
          <div className="space-y-3">
            {(stationList.length > 0 ? stationList : stations).map((station) => {
              const stats = stationStats[station.id || station._id];
              return (
                <div key={station.id || station._id} className="border-b border-gray-600 pb-2 last:border-b-0">
                  <div className="flex justify-between items-center mb-1">
                    <span className="text-gray-300 text-sm font-medium">{station.name}</span>
                    <span className="text-cyan-400 text-xs">
                      {stats ? `${stats.occupied}/${stats.total}` : '加载中...'}
                    </span>
                  </div>
                  {stats && (
                    <>
                      <Progress
                        percent={stats.total > 0 ? Math.round((stats.occupied / stats.total) * 100) : 0}
                        showInfo={false}
                        strokeColor="#06b6d4"
                        trailColor="#374151"
                        size="small"
                      />
                      <div className="flex justify-between text-xs text-gray-400 mt-1">
                        <span>已配置: {stats.occupied}</span>
                        <span>空缺: {stats.vacant}</span>
                        <span>占用率: {stats.occupancyRate}%</span>
                      </div>
                    </>
                  )}
                </div>
              );
            })}
          </div>
        </Card>

        {/* 员工负荷 Top 5 */}
        <Card
          title="员工负荷 (Top 5)"
          className="bg-slate-800 border-gray-600 mx-4 mb-4"
          styles={{ body: { padding: '16px' } }}
        >
          <div className="space-y-3">
            {topLoadedEmployees.map((emp, index) => (
              <div key={index}>
                <div className="flex justify-between items-center mb-1">
                  <span className="text-gray-300 text-sm">{emp.name}</span>
                  <span className="text-cyan-400 text-sm">{emp.value}</span>
                </div>
                <Progress
                  percent={emp.value > 0 ? Math.min((emp.value / Math.max(...topLoadedEmployees.map(e => e.value), 1)) * 100, 100) : 0}
                  showInfo={false}
                  strokeColor="#06b6d4"
                  trailColor="#374151"
                  size="small"
                />
              </div>
            ))}
          </div>
        </Card>
      </div>

      {/* 调度模态框 */}
      <DispatchModal
        isOpen={isDispatchModalOpen}
        employee={selectedDispatchEmployee}
        stations={stationList.length > 0 ? stationList : stations}
        onClose={() => {
          setIsDispatchModalOpen(false);
          setSelectedDispatchEmployee(null);
        }}
        onSubmit={(dispatchData) => {
          console.log('提交调度申请:', dispatchData);
          message.success('调度申请已提交');
          setIsDispatchModalOpen(false);
          setSelectedDispatchEmployee(null);
        }}
      />
    </div>
  );
};

export default OverviewTab;