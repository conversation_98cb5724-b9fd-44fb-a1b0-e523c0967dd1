import mongoose, { Schema, Document } from 'mongoose';

// 岗位模板接口
export interface IPosition {
  _id?: string;
  positionName: string;        // 岗位名称
  department: string;          // 所属部门
  skillRequirements: string[]; // 技能要求
  certificationRequirements: string[]; // 证书要求
  description?: string;        // 岗位描述
  level: 'junior' | 'senior' | 'expert'; // 岗位级别
  isActive: boolean;           // 是否启用
  createdAt?: Date;
  updatedAt?: Date;
}

export interface PositionDocument extends Omit<IPosition, '_id'>, Document {}

const PositionSchema = new Schema<PositionDocument>({
  positionName: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100
  },
  department: {
    type: String,
    required: true,
    enum: ['management', 'production', 'equipment', 'safety', 'maintenance']
  },
  skillRequirements: [{
    type: String,
    trim: true
  }],
  certificationRequirements: [{
    type: String,
    trim: true
  }],
  description: {
    type: String,
    trim: true,
    maxlength: 500
  },
  level: {
    type: String,
    required: true,
    enum: ['junior', 'senior', 'expert'],
    default: 'junior'
  },
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// 索引
PositionSchema.index({ positionName: 1, department: 1 }, { unique: true });
PositionSchema.index({ department: 1 });
PositionSchema.index({ level: 1 });
PositionSchema.index({ isActive: 1 });

// 虚拟字段
PositionSchema.virtual('id').get(function(this: any) {
  return this._id.toString();
});

export const Position = mongoose.model<PositionDocument>('Position', PositionSchema);