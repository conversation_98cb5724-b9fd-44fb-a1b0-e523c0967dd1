import express from 'express';
import {
  getAllConcurrentPositions,
  createConcurrentPosition,
  updateConcurrentPosition,
  endConcurrentPosition,
  getEmployeeConcurrentPositions,
  getStationConcurrentEmployees,
  getPositionConcurrentEmployees,
  getConcurrentPositionStats,
  deleteConcurrentPosition
} from '../controllers/concurrentPositionController';

const router = express.Router();

// 获取所有兼职记录
router.get('/', getAllConcurrentPositions);

// 创建兼职记录
router.post('/', createConcurrentPosition);

// 更新兼职记录
router.put('/:id', updateConcurrentPosition);

// 结束兼职
router.patch('/:id/end', endConcurrentPosition);

// 删除兼职记录
router.delete('/:id', deleteConcurrentPosition);

// 获取员工的兼职记录
router.get('/employee/:employeeId', getEmployeeConcurrentPositions);

// 获取电站的兼职员工
router.get('/station/:stationId', getStationConcurrentEmployees);

// 获取岗位的兼职员工
router.get('/position/:positionId', getPositionConcurrentEmployees);

// 获取兼职统计信息
router.get('/stats', getConcurrentPositionStats);

export default router;