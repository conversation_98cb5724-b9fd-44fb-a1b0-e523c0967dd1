@tailwind base;
@tailwind components;
@tailwind utilities;

/* 自定义样式 */
@layer base {
  body {
    @apply bg-slate-900 text-white font-sans;
  }
}

@layer components {
  .text-shadow-cyan {
    text-shadow: 0 0 8px rgba(0, 255, 255, 0.7);
  }
  
  .bg-grid-pattern {
    background-image: linear-gradient(rgba(0, 180, 255, 0.1) 1px, transparent 1px), 
                      linear-gradient(90deg, rgba(0, 180, 255, 0.1) 1px, transparent 1px);
    background-size: 20px 20px;
  }
  
  .clip-path-hexagon {
    clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
  }
  
  .futuristic-card {
    @apply bg-slate-900/50 backdrop-blur-sm border border-cyan-400/30 rounded-lg p-4 relative overflow-hidden shadow-lg shadow-cyan-500/10;
  }
  
  .futuristic-card::before {
    content: '';
    @apply absolute top-0 left-0 w-full h-full bg-grid-pattern opacity-10 pointer-events-none;
  }
  
  .futuristic-card::after {
    content: '';
    @apply absolute -top-1/4 -left-1/4 w-1/2 h-1/2 bg-cyan-500/20 rounded-full filter blur-3xl opacity-50 pointer-events-none;
  }

  /* 暗色主题表格样式 */
  .dark-table .ant-table {
    background: transparent;
    color: #ffffff;
  }
  
  .dark-table .ant-table-thead > tr > th {
    background: #374151;
    color: #ffffff;
    border-bottom: 1px solid #4b5563;
    font-weight: 600;
  }
  
  .dark-table .ant-table-tbody > tr > td {
    background: transparent;
    color: #e5e7eb;
    border-bottom: 1px solid #374151;
  }
  
  .dark-table .ant-table-tbody > tr:hover > td {
    background: #374151 !important;
  }
  
  .dark-table .ant-table-tbody > tr.ant-table-row-selected > td {
    background: #1e40af !important;
  }
  
  .dark-table .ant-table-placeholder {
    background: transparent;
    color: #9ca3af;
  }
  
  .dark-table .ant-empty-description {
    color: #9ca3af;
  }
}

@layer utilities {
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
  
  /* 自定义滚动条样式 */
  .scrollbar-custom {
    scrollbar-width: thin;
    scrollbar-color: #cbd5e1 #f1f5f9;
  }
  
  .scrollbar-custom::-webkit-scrollbar {
    width: 8px;
  }
  
  .scrollbar-custom::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 4px;
  }
  
  .scrollbar-custom::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 4px;
  }
  
  .scrollbar-custom::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
  }
  
  /* 确保所有overflow-y-auto元素都有可见的滚动条 */
  .overflow-y-auto {
    scrollbar-width: thin;
    scrollbar-color: #cbd5e1 #f1f5f9;
  }
  
  .overflow-y-auto::-webkit-scrollbar {
    width: 8px;
  }
  
  .overflow-y-auto::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 4px;
  }
  
  .overflow-y-auto::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 4px;
  }
  
  .overflow-y-auto::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
  }
}