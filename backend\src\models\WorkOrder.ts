import mongoose, { Document, Schema } from 'mongoose';

export interface IWorkOrder extends Document {
  id: string;
  title: string;
  description: string;
  stationId: mongoose.Types.ObjectId;
  createdBy: string;
  assignedTo?: string;
  status: 'OPEN' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED';
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
  type: '报修' | '定期维护' | '预防性维护' | '应急处理' | '其他';
  createdAt: Date;
  updatedAt: Date;
  completedAt?: Date;
  estimatedDuration?: number; // 预计工时（小时）
  actualDuration?: number; // 实际工时（小时）
  notes?: string;
  attachments?: string[];
}

const WorkOrderSchema: Schema = new Schema({
  title: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    required: true
  },
  stationId: {
    type: Schema.Types.ObjectId,
    ref: 'PowerStation',
    required: true
  },
  createdBy: {
    type: String,
    required: true
  },
  assignedTo: {
    type: String
  },
  status: {
    type: String,
    enum: ['OPEN', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED'],
    default: 'OPEN'
  },
  priority: {
    type: String,
    enum: ['LOW', 'MEDIUM', 'HIGH', 'URGENT'],
    default: 'MEDIUM'
  },
  type: {
    type: String,
    enum: ['报修', '定期维护', '预防性维护', '应急处理', '其他'],
    required: true
  },
  completedAt: {
    type: Date
  },
  estimatedDuration: {
    type: Number
  },
  actualDuration: {
    type: Number
  },
  notes: {
    type: String
  },
  attachments: [{
    type: String
  }]
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// 添加虚拟字段
WorkOrderSchema.virtual('id').get(function(this: any) {
  return this._id.toHexString();
});

// 确保虚拟字段被序列化
WorkOrderSchema.set('toJSON', {
  virtuals: true,
  transform: function(doc: any, ret: any) {
    ret.id = ret._id;
    delete ret._id;
    delete ret.__v;
    return ret;
  }
});

export default mongoose.model<IWorkOrder>('WorkOrder', WorkOrderSchema);
