import { apiClient } from './api';

// 枚举值接口定义
export interface EnumOption {
  value: string;
  label: string;
}

export interface EnumData {
  employeeStatus?: EnumOption[];
  employeeType?: EnumOption[];
  department?: EnumOption[];
  position?: EnumOption[];
  dispatchType?: EnumOption[];
  dispatchStatus?: EnumOption[];
  dispatchSource?: EnumOption[];
  riskLevel?: EnumOption[];
  urgency?: EnumOption[];
  stationType?: EnumOption[];
  stationStatus?: EnumOption[];
  userRole?: EnumOption[];
  workOrderStatus?: EnumOption[];
  workOrderPriority?: EnumOption[];
  workOrderType?: EnumOption[];
}

class EnumService {
  private enumCache: EnumData | null = null;
  private cacheTimestamp: number = 0;
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5分钟缓存

  /**
   * 获取所有枚举值
   */
  async getAllEnums(): Promise<EnumData> {
    // 检查缓存是否有效
    const now = Date.now();
    if (this.enumCache && (now - this.cacheTimestamp) < this.CACHE_DURATION) {
      return this.enumCache;
    }

    try {
      const response = await apiClient.request<EnumData>('/enums', { method: 'GET' });
      this.enumCache = response;
      this.cacheTimestamp = now;
      return response;
    } catch (error) {
      console.error('获取枚举值失败:', error);
      // 如果有缓存，返回缓存数据
      if (this.enumCache) {
        return this.enumCache;
      }
      // 否则返回默认值
      return this.getDefaultEnums();
    }
  }

  /**
   * 获取特定类型的枚举值
   */
  async getEnumByType(type: keyof EnumData): Promise<EnumOption[]> {
    try {
      const response = await apiClient.request<EnumOption[]>(`/enums/${type}`, { method: 'GET' });
      return response;
    } catch (error) {
      console.error(`获取枚举值 ${type} 失败:`, error);
      const allEnums = await this.getAllEnums();
      return allEnums[type] || [];
    }
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    this.enumCache = null;
    this.cacheTimestamp = 0;
  }

  /**
   * 获取默认枚举值（作为后备）
   */
  private getDefaultEnums(): EnumData {
    return {
      employeeStatus: [
        { value: 'active', label: '在职' },
        { value: 'learning', label: '学习中' },
        { value: 'inactive', label: '离职' },
        { value: 'vacant', label: '空缺' }
      ],
      employeeType: [
        { value: 'full_time', label: '全职员工' },
        { value: 'part_time', label: '兼职员工' },
        { value: 'intern', label: '实习生' },
        { value: 'external', label: '外聘人员' },
        { value: 'borrowed', label: '借调人员' }
      ],
      department: [
        { value: 'management', label: '管理部门' },
        { value: 'production', label: '生产部门' },
        { value: 'equipment', label: '设备部门' },
        { value: 'safety', label: '安全部门' },
        { value: 'maintenance', label: '维护部门' }
      ],
      position: [
        { value: 'engineer', label: '工程师' },
        { value: 'technician', label: '技术员' },
        { value: 'operator', label: '操作员' },
        { value: 'manager', label: '管理员' },
        { value: 'supervisor', label: '主管' },
        { value: 'specialist', label: '专家' }
      ],
      dispatchType: [
        { value: 'temporary', label: '临时调度' },
        { value: 'permanent', label: '永久调度' },
        { value: 'emergency', label: '紧急调度' }
      ],
      dispatchStatus: [
        { value: 'pending', label: '待审批' },
        { value: 'approved', label: '已审批' },
        { value: 'active', label: '执行中' },
        { value: 'completed', label: '已完成' },
        { value: 'cancelled', label: '已取消' },
        { value: 'rejected', label: '已拒绝' }
      ],
      dispatchSource: [
        { value: 'manual', label: '手动创建' },
        { value: 'ai_query', label: 'AI查询生成' },
        { value: 'ai_recommend', label: 'AI智能推荐' }
      ],
      riskLevel: [
        { value: 'low', label: '低风险' },
        { value: 'medium', label: '中等风险' },
        { value: 'high', label: '高风险' },
        { value: 'critical', label: '严重风险' }
      ],
      urgency: [
        { value: 'LOW', label: '低' },
        { value: 'MEDIUM', label: '中' },
        { value: 'HIGH', label: '高' },
        { value: 'URGENT', label: '紧急' }
      ],
      stationType: [
        { value: 'solar', label: '光伏发电' },
        { value: 'wind', label: '风力发电' },
        { value: 'hydro', label: '水力发电' },
        { value: 'thermal', label: '火力发电' }
      ],
      stationStatus: [
        { value: 'active', label: '运行中' },
        { value: 'maintenance', label: '维护中' },
        { value: 'offline', label: '离线' }
      ],
      userRole: [
        { value: 'admin', label: '管理员' },
        { value: 'operator', label: '操作员' },
        { value: 'viewer', label: '查看者' }
      ],
      workOrderStatus: [
        { value: 'OPEN', label: '待处理' },
        { value: 'IN_PROGRESS', label: '处理中' },
        { value: 'COMPLETED', label: '已完成' },
        { value: 'CANCELLED', label: '已取消' }
      ],
      workOrderPriority: [
        { value: 'LOW', label: '低' },
        { value: 'MEDIUM', label: '中' },
        { value: 'HIGH', label: '高' },
        { value: 'URGENT', label: '紧急' }
      ],
      workOrderType: [
        { value: '报修', label: '报修' },
        { value: '定期维护', label: '定期维护' },
        { value: '预防性维护', label: '预防性维护' },
        { value: '应急处理', label: '应急处理' },
        { value: '其他', label: '其他' }
      ]
    };
  }
}

// 创建单例实例
export const enumService = new EnumService();

// 导出便捷方法
export const getAllEnums = () => enumService.getAllEnums();
export const getEnumByType = (type: keyof EnumData) => enumService.getEnumByType(type);
export const clearEnumCache = () => enumService.clearCache();