import { Request, Response } from 'express';
import { DispatchType, DispatchStatus, DispatchSource, RiskLevel } from '../models/DispatchRecord';

/**
 * 获取所有枚举值
 */
export const getAllEnums = async (req: Request, res: Response): Promise<void> => {
  try {
    const enums = {
      // 员工相关枚举
      employeeStatus: [
        { value: 'active', label: '在职' },
        { value: 'learning', label: '学习中' },
        { value: 'inactive', label: '离职' },
        { value: 'vacant', label: '空缺' },
        { value: 'pending', label: '待入职' },
        { value: 'external_contract', label: '外委' },
        { value: 'resigned', label: '已离职' }
      ],
      
      employeeType: [
        { value: 'full_time', label: '全职员工' },
        { value: 'part_time', label: '兼职员工' },
        { value: 'intern', label: '实习生' },
        { value: 'external', label: '外聘人员' },
        { value: 'borrowed', label: '借调人员' },
        { value: 'concurrent', label: '兼职人员' },
        { value: 'pending', label: '待入职' }
      ],
      
      department: [
        { value: 'management', label: '管理部门' },
        { value: 'production', label: '生产部门' },
        { value: 'equipment', label: '设备部门' },
        { value: 'safety', label: '安全部门' },
        { value: 'maintenance', label: '维护部门' }
      ],
      
      // 岗位相关枚举
      position: [
        { value: 'engineer', label: '工程师' },
        { value: 'technician', label: '技术员' },
        { value: 'operator', label: '操作员' },
        { value: 'manager', label: '管理员' },
        { value: 'supervisor', label: '主管' },
        { value: 'specialist', label: '专家' },
        { value: 'maintenance_engineer', label: '维护工程师' },
        { value: 'safety_officer', label: '安全员' },
        { value: 'quality_inspector', label: '质检员' },
        { value: 'shift_leader', label: '班长' }
      ],
      
      // 调度相关枚举
      dispatchType: [
        { value: DispatchType.TEMPORARY, label: '临时调度' },
        { value: DispatchType.PERMANENT, label: '永久调度' },
        { value: DispatchType.EMERGENCY, label: '紧急调度' }
      ],
      
      dispatchStatus: [
        { value: DispatchStatus.PENDING, label: '待审批' },
        { value: DispatchStatus.APPROVED, label: '已审批' },
        { value: DispatchStatus.ACTIVE, label: '执行中' },
        { value: DispatchStatus.COMPLETED, label: '已完成' },
        { value: DispatchStatus.CANCELLED, label: '已取消' },
        { value: DispatchStatus.REJECTED, label: '已拒绝' }
      ],
      
      dispatchSource: [
        { value: DispatchSource.MANUAL, label: '手动创建' },
        { value: DispatchSource.AI_QUERY, label: 'AI查询生成' },
        { value: DispatchSource.AI_RECOMMEND, label: 'AI智能推荐' }
      ],
      
      riskLevel: [
        { value: RiskLevel.LOW, label: '低风险' },
        { value: RiskLevel.MEDIUM, label: '中等风险' },
        { value: RiskLevel.HIGH, label: '高风险' },
        { value: RiskLevel.CRITICAL, label: '严重风险' }
      ],
      
      // 紧急程度
      urgency: [
        { value: 'LOW', label: '低' },
        { value: 'MEDIUM', label: '中' },
        { value: 'HIGH', label: '高' },
        { value: 'URGENT', label: '紧急' }
      ],
      
      // 电站相关枚举
      stationType: [
        { value: 'solar', label: '光伏发电' },
        { value: 'wind', label: '风力发电' },
        { value: 'hydro', label: '水力发电' },
        { value: 'thermal', label: '火力发电' },
        { value: 'nuclear', label: '核能发电' },
        { value: 'hybrid', label: '混合发电' }
      ],
      
      stationStatus: [
        { value: 'active', label: '运行中' },
        { value: 'maintenance', label: '维护中' },
        { value: 'offline', label: '离线' }
      ],
      
      // 用户角色
      userRole: [
        { value: 'admin', label: '管理员' },
        { value: 'operator', label: '操作员' },
        { value: 'viewer', label: '查看者' }
      ],
      
      // 工单相关枚举
      workOrderStatus: [
        { value: 'OPEN', label: '待处理' },
        { value: 'IN_PROGRESS', label: '处理中' },
        { value: 'COMPLETED', label: '已完成' },
        { value: 'CANCELLED', label: '已取消' }
      ],
      
      workOrderPriority: [
        { value: 'LOW', label: '低' },
        { value: 'MEDIUM', label: '中' },
        { value: 'HIGH', label: '高' },
        { value: 'URGENT', label: '紧急' }
      ],
      
      workOrderType: [
        { value: '报修', label: '报修' },
        { value: '定期维护', label: '定期维护' },
        { value: '预防性维护', label: '预防性维护' },
        { value: '应急处理', label: '应急处理' },
        { value: '其他', label: '其他' }
      ]
    };

    res.json({
      success: true,
      data: enums
    });
  } catch (error) {
    console.error('获取枚举值失败:', error);
    res.status(500).json({
      success: false,
      message: '获取枚举值失败',
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
};

/**
 * 获取特定类型的枚举值
 */
export const getEnumByType = async (req: Request, res: Response): Promise<void> => {
  try {
    const { type } = req.params;
    
    // 调用getAllEnums获取所有枚举值
    const mockReq = {} as Request;
    const mockRes = {
      json: (data: any) => data
    } as any;
    
    await getAllEnums(mockReq, mockRes);
    const allEnums = mockRes.json().data;
    
    if (!allEnums[type]) {
      res.status(404).json({
        success: false,
        message: `枚举类型 '${type}' 不存在`
      });
      return;
    }
    
    res.json({
      success: true,
      data: allEnums[type]
    });
  } catch (error) {
    console.error('获取特定枚举值失败:', error);
    res.status(500).json({
      success: false,
      message: '获取枚举值失败',
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
};