import { Request, Response } from 'express';
import { ApiResponse } from '../types/index';
import { ConcurrentPosition } from '../models/ConcurrentPosition';
import { Employee } from '../models/Employee';
import { Position } from '../models/Position';
import { PowerStation } from '../models/PowerStation';
import { logger } from '../utils/logger';

// 获取所有兼职记录
export const getAllConcurrentPositions = async (req: Request, res: Response): Promise<void> => {
  try {
    const { status, employeeId, stationId, positionId, page = 1, limit = 20 } = req.query;
    
    const filter: any = {};
    if (status) filter.status = status;
    if (employeeId) filter.employeeId = employeeId;
    if (stationId) filter.stationId = stationId;
    if (positionId) filter.positionId = positionId;
    
    const skip = (Number(page) - 1) * Number(limit);
    
    const [concurrentPositions, total] = await Promise.all([
      ConcurrentPosition.find(filter)
        .populate('employeeId', 'name employeeType status homeStationId currentStationId')
        .populate('stationId', 'name')
        .populate('positionId', 'positionName department level')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(Number(limit)),
      ConcurrentPosition.countDocuments(filter)
    ]);
    
    const response: ApiResponse<{ records: typeof concurrentPositions, pagination: any }> = {
      success: true,
      data: {
        records: concurrentPositions,
        pagination: {
          current: Number(page),
          pageSize: Number(limit),
          total,
          pages: Math.ceil(total / Number(limit))
        }
      }
    };
    
    res.status(200).json(response);
  } catch (error) {
    logger.error('获取兼职记录失败:', error);
    const response: ApiResponse = {
      success: false,
      message: '获取兼职记录失败'
    };
    res.status(500).json(response);
  }
};

// 创建兼职记录
export const createConcurrentPosition = async (req: Request, res: Response): Promise<void> => {
  try {
    const {
      employeeId,
      stationId,
      positionId,
      startDate,
      endDate,
      workloadPercentage,
      priority,
      reason,
      notes
    } = req.body;
    
    // 验证员工、电站和岗位是否存在
    const [employee, station, position] = await Promise.all([
      Employee.findById(employeeId),
      PowerStation.findById(stationId),
      Position.findById(positionId)
    ]);
    
    if (!employee) {
      const response: ApiResponse = {
        success: false,
        message: '员工不存在'
      };
      res.status(404).json(response);
      return;
    }
    
    if (!station) {
      const response: ApiResponse = {
        success: false,
        message: '电站不存在'
      };
      res.status(404).json(response);
      return;
    }
    
    if (!position) {
      const response: ApiResponse = {
        success: false,
        message: '岗位模板不存在'
      };
      res.status(404).json(response);
      return;
    }
    
    // 检查是否已存在相同的活跃兼职记录
    const existingRecord = await ConcurrentPosition.findOne({
      employeeId,
      stationId,
      positionId,
      status: 'active'
    });
    
    if (existingRecord) {
      const response: ApiResponse = {
        success: false,
        message: '该员工在此电站的此岗位已有活跃的兼职记录'
      };
      res.status(400).json(response);
      return;
    }
    
    const concurrentPosition = new ConcurrentPosition({
      employeeId,
      stationId,
      positionId,
      startDate: new Date(startDate),
      endDate: endDate ? new Date(endDate) : undefined,
      workloadPercentage: workloadPercentage || 50,
      priority: priority || 'medium',
      reason,
      notes,
      status: 'active'
    });
    
    await concurrentPosition.save();
    
    // 填充关联数据
    await concurrentPosition.populate([
      { path: 'employeeId', select: 'name employeeType status homeStationId currentStationId' },
      { path: 'stationId', select: 'name' },
      { path: 'positionId', select: 'positionName department level' }
    ]);
    
    const response: ApiResponse<typeof concurrentPosition> = {
      success: true,
      message: '兼职记录创建成功',
      data: concurrentPosition
    };
    
    res.status(201).json(response);
  } catch (error) {
    logger.error('创建兼职记录失败:', error);
    const response: ApiResponse = {
      success: false,
      message: '创建兼职记录失败'
    };
    res.status(500).json(response);
  }
};

// 更新兼职记录
export const updateConcurrentPosition = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const updateData = req.body;
    
    const concurrentPosition = await ConcurrentPosition.findByIdAndUpdate(
      id,
      { ...updateData, updatedAt: new Date() },
      { new: true, runValidators: true }
    ).populate([
      { path: 'employeeId', select: 'name employeeType status homeStationId currentStationId' },
      { path: 'stationId', select: 'name' },
      { path: 'positionId', select: 'positionName department level' }
    ]);
    
    if (!concurrentPosition) {
      const response: ApiResponse = {
        success: false,
        message: '兼职记录不存在'
      };
      res.status(404).json(response);
      return;
    }
    
    const response: ApiResponse<typeof concurrentPosition> = {
      success: true,
      message: '兼职记录更新成功',
      data: concurrentPosition
    };
    
    res.status(200).json(response);
  } catch (error) {
    logger.error('更新兼职记录失败:', error);
    const response: ApiResponse = {
      success: false,
      message: '更新兼职记录失败'
    };
    res.status(500).json(response);
  }
};

// 结束兼职
export const endConcurrentPosition = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const { endReason, notes } = req.body;
    
    // 先获取当前记录
    const existingPosition = await ConcurrentPosition.findById(id);
    if (!existingPosition) {
      const response: ApiResponse = {
        success: false,
        message: '兼职记录不存在'
      };
      res.status(404).json(response);
      return;
    }
    
    const concurrentPosition = await ConcurrentPosition.findByIdAndUpdate(
      id,
      {
        status: 'completed',
        actualEndDate: new Date(),
        endReason,
        notes: notes || existingPosition.notes,
        updatedAt: new Date()
      },
      { new: true }
    ).populate([
      { path: 'employeeId', select: 'name employeeType status homeStationId currentStationId' },
      { path: 'stationId', select: 'name' },
      { path: 'positionId', select: 'positionName department level' }
    ]);
    
    const response: ApiResponse<typeof concurrentPosition> = {
      success: true,
      message: '兼职已结束',
      data: concurrentPosition
    };
    
    res.status(200).json(response);
  } catch (error) {
    logger.error('结束兼职失败:', error);
    const response: ApiResponse = {
      success: false,
      message: '结束兼职失败'
    };
    res.status(500).json(response);
  }
};

// 获取员工的兼职记录
export const getEmployeeConcurrentPositions = async (req: Request, res: Response): Promise<void> => {
  try {
    const { employeeId } = req.params;
    const { status } = req.query;
    
    const filter: any = { employeeId };
    if (status) filter.status = status;
    
    const concurrentPositions = await ConcurrentPosition.find(filter)
      .populate('stationId', 'name')
      .populate('positionId', 'positionName department level')
      .sort({ createdAt: -1 });
    
    const response: ApiResponse<typeof concurrentPositions> = {
      success: true,
      data: concurrentPositions
    };
    
    res.status(200).json(response);
  } catch (error) {
    logger.error('获取员工兼职记录失败:', error);
    const response: ApiResponse = {
      success: false,
      message: '获取员工兼职记录失败'
    };
    res.status(500).json(response);
  }
};

// 获取电站的兼职员工
export const getStationConcurrentEmployees = async (req: Request, res: Response): Promise<void> => {
  try {
    const { stationId } = req.params;
    const { status = 'active' } = req.query;
    
    const concurrentPositions = await ConcurrentPosition.find({
      stationId,
      status
    })
      .populate('employeeId', 'name employeeType status homeStationId currentStationId')
      .populate('positionId', 'positionName department level')
      .sort({ priority: 1, createdAt: -1 });
    
    const response: ApiResponse<typeof concurrentPositions> = {
      success: true,
      data: concurrentPositions
    };
    
    res.status(200).json(response);
  } catch (error) {
    logger.error('获取电站兼职员工失败:', error);
    const response: ApiResponse = {
      success: false,
      message: '获取电站兼职员工失败'
    };
    res.status(500).json(response);
  }
};

// 获取岗位的兼职员工
export const getPositionConcurrentEmployees = async (req: Request, res: Response): Promise<void> => {
  try {
    const { positionId } = req.params;
    const { stationId, status = 'active' } = req.query;
    
    const filter: any = { positionId, status };
    if (stationId) filter.stationId = stationId;
    
    const concurrentPositions = await ConcurrentPosition.find(filter)
      .populate('employeeId', 'name employeeType status homeStationId currentStationId')
      .populate('stationId', 'name')
      .sort({ priority: 1, createdAt: -1 });
    
    const response: ApiResponse<typeof concurrentPositions> = {
      success: true,
      data: concurrentPositions
    };
    
    res.status(200).json(response);
  } catch (error) {
    logger.error('获取岗位兼职员工失败:', error);
    const response: ApiResponse = {
      success: false,
      message: '获取岗位兼职员工失败'
    };
    res.status(500).json(response);
  }
};

// 获取兼职统计信息
export const getConcurrentPositionStats = async (req: Request, res: Response): Promise<void> => {
  try {
    const { stationId, startDate, endDate } = req.query;
    
    const dateFilter: any = {};
    if (startDate || endDate) {
      dateFilter.createdAt = {};
      if (startDate) dateFilter.createdAt.$gte = new Date(startDate as string);
      if (endDate) dateFilter.createdAt.$lte = new Date(endDate as string);
    }
    
    let matchCondition: any = dateFilter;
    if (stationId) {
      matchCondition.stationId = stationId;
    }
    
    const [
      totalRecords,
      activeRecords,
      completedRecords,
      byStatus,
      byPriority,
      byPosition
    ] = await Promise.all([
      ConcurrentPosition.countDocuments(matchCondition),
      ConcurrentPosition.countDocuments({ ...matchCondition, status: 'active' }),
      ConcurrentPosition.countDocuments({ ...matchCondition, status: 'completed' }),
      ConcurrentPosition.aggregate([
        { $match: matchCondition },
        { $group: { _id: '$status', count: { $sum: 1 } } }
      ]),
      ConcurrentPosition.aggregate([
        { $match: matchCondition },
        { $group: { _id: '$priority', count: { $sum: 1 } } }
      ]),
      ConcurrentPosition.aggregate([
        { $match: matchCondition },
        { $lookup: { from: 'positions', localField: 'positionId', foreignField: '_id', as: 'position' } },
        { $unwind: '$position' },
        { $group: { _id: '$position.positionName', count: { $sum: 1 } } }
      ])
    ]);
    
    const stats = {
      total: totalRecords,
      active: activeRecords,
      completed: completedRecords,
      byStatus: byStatus.reduce((acc: any, item: any) => {
        acc[item._id] = item.count;
        return acc;
      }, {}),
      byPriority: byPriority.reduce((acc: any, item: any) => {
        acc[item._id] = item.count;
        return acc;
      }, {}),
      byPosition: byPosition.reduce((acc: any, item: any) => {
        acc[item._id] = item.count;
        return acc;
      }, {})
    };
    
    const response: ApiResponse<typeof stats> = {
      success: true,
      data: stats
    };
    
    res.status(200).json(response);
  } catch (error) {
    logger.error('获取兼职统计信息失败:', error);
    const response: ApiResponse = {
      success: false,
      message: '获取兼职统计信息失败'
    };
    res.status(500).json(response);
  }
};

// 删除兼职记录
export const deleteConcurrentPosition = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    
    const concurrentPosition = await ConcurrentPosition.findById(id);
    
    if (!concurrentPosition) {
      const response: ApiResponse = {
        success: false,
        message: '兼职记录不存在'
      };
      res.status(404).json(response);
      return;
    }
    
    // 只允许删除非活跃状态的记录
    if (concurrentPosition.status === 'active') {
      const response: ApiResponse = {
        success: false,
        message: '不能删除活跃状态的兼职记录，请先结束兼职'
      };
      res.status(400).json(response);
      return;
    }
    
    await ConcurrentPosition.findByIdAndDelete(id);
    
    const response: ApiResponse = {
      success: true,
      message: '兼职记录删除成功'
    };
    
    res.status(200).json(response);
  } catch (error) {
    logger.error('删除兼职记录失败:', error);
    const response: ApiResponse = {
      success: false,
      message: '删除兼职记录失败'
    };
    res.status(500).json(response);
  }
};