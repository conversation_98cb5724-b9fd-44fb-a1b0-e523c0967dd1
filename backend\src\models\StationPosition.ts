import mongoose, { Schema, Document, Types } from 'mongoose';

// 电站岗位配置接口
export interface IStationPosition {
  _id?: string;
  stationId: string;          // 电站ID
  positionId: string;         // 岗位模板ID
  instance: number;           // 岗位实例编号（同一岗位可能有多个）
  employeeId?: string;        // 当前员工ID
  status: 'occupied' | 'vacant' | 'not_configured' | 'pending'; // 配置状态
  priority: 'high' | 'medium' | 'low'; // 优先级
  source?: 'recruitment' | 'outsourcing' | 'internal' | 'undecided'; // 人员来源
  outsourcingCompany?: string; // 外包公司
  notes?: string;             // 备注
  createdAt?: Date;
  updatedAt?: Date;
}

export interface StationPositionDocument extends Omit<IStationPosition, '_id' | 'stationId' | 'positionId' | 'employeeId'>, Document {
  stationId: Types.ObjectId;
  positionId: Types.ObjectId;
  employeeId?: Types.ObjectId;
}

const StationPositionSchema = new Schema<StationPositionDocument>({
  stationId: {
    type: Schema.Types.ObjectId,
    ref: 'PowerStation',
    required: true
  },
  positionId: {
    type: Schema.Types.ObjectId,
    ref: 'Position',
    required: true
  },
  instance: {
    type: Number,
    required: true,
    default: 1,
    min: 1
  },
  employeeId: {
    type: Schema.Types.ObjectId,
    ref: 'Employee'
  },
  status: {
    type: String,
    required: true,
    enum: ['occupied', 'vacant', 'not_configured', 'pending'],
    default: 'not_configured'
  },
  priority: {
    type: String,
    required: true,
    enum: ['high', 'medium', 'low'],
    default: 'medium'
  },
  source: {
    type: String,
    enum: ['recruitment', 'outsourcing', 'internal', 'undecided']
  },
  outsourcingCompany: {
    type: String,
    trim: true
  },
  notes: {
    type: String,
    trim: true,
    maxlength: 500
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// 索引
StationPositionSchema.index({ stationId: 1, positionId: 1, instance: 1 }, { unique: true });
StationPositionSchema.index({ stationId: 1 });
StationPositionSchema.index({ positionId: 1 });
StationPositionSchema.index({ employeeId: 1 });
StationPositionSchema.index({ status: 1 });
StationPositionSchema.index({ priority: 1 });

// 虚拟字段
StationPositionSchema.virtual('id').get(function(this: any) {
  return this._id.toString();
});

export const StationPosition = mongoose.model<StationPositionDocument>('StationPosition', StationPositionSchema);