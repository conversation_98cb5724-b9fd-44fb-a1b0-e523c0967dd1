import mongoose from 'mongoose';
import WorkOrder from '../models/WorkOrder';
import { PowerStation } from '../models/PowerStation';

// 连接数据库
const connectDB = async () => {
  try {
    await mongoose.connect('mongodb://localhost:27017/power_station_management');
    console.log('MongoDB 连接成功');
  } catch (error) {
    console.error('MongoDB 连接失败:', error);
    process.exit(1);
  }
};

// 示例工单数据
const sampleWorkOrders = [
  {
    title: '3号逆变器故障',
    description: '3号逆变器发出异响，功率输出不稳定，需要立即检修。可能是内部风扇故障或者功率模块问题。',
    type: '报修',
    priority: 'HIGH',
    status: 'IN_PROGRESS',
    createdBy: '张伟',
    assignedTo: '李娜',
    estimatedDuration: 4,
    notes: '已联系厂家技术支持，预计需要更换风扇模块'
  },
  {
    title: '光伏板季度清洁',
    description: 'C区光伏板积灰严重，影响发电效率，需要进行清洁维护。',
    type: '定期维护',
    priority: 'MEDIUM',
    status: 'OPEN',
    createdBy: '系统',
    estimatedDuration: 8,
    notes: '需要安排专业清洁队伍'
  },
  {
    title: '变压器年度检修',
    description: '主变压器年度例行检修，包括绝缘测试、油样分析、接触器检查等。',
    type: '预防性维护',
    priority: 'MEDIUM',
    status: 'OPEN',
    createdBy: '王工',
    estimatedDuration: 16,
    notes: '需要停机检修，已申请停机时间窗口'
  },
  {
    title: '监控系统通讯故障',
    description: '监控系统与部分设备通讯中断，无法获取实时数据。',
    type: '报修',
    priority: 'HIGH',
    status: 'OPEN',
    createdBy: '李技术员',
    estimatedDuration: 2,
    notes: '可能是网络设备故障'
  },
  {
    title: '安全防护设施检查',
    description: '月度安全防护设施检查，包括围栏、警示标识、消防设备等。',
    type: '定期维护',
    priority: 'LOW',
    status: 'COMPLETED',
    createdBy: '安全员',
    assignedTo: '安全检查组',
    estimatedDuration: 4,
    actualDuration: 3.5,
    completedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2天前完成
    notes: '检查完成，所有设施正常'
  },
  {
    title: '电缆沟清理',
    description: '电缆沟内积水和杂物清理，确保电缆安全运行。',
    type: '定期维护',
    priority: 'MEDIUM',
    status: 'IN_PROGRESS',
    createdBy: '维护班长',
    assignedTo: '维护组',
    estimatedDuration: 6,
    notes: '雨季后例行清理'
  },
  {
    title: '备用发电机测试',
    description: '备用柴油发电机月度启动测试，确保应急状态下能正常工作。',
    type: '预防性维护',
    priority: 'MEDIUM',
    status: 'OPEN',
    createdBy: '设备管理员',
    estimatedDuration: 2,
    notes: '需要检查燃油系统和启动系统'
  },
  {
    title: '继电保护定值核查',
    description: '继电保护装置定值核查和校验，确保保护功能正常。',
    type: '预防性维护',
    priority: 'HIGH',
    status: 'OPEN',
    createdBy: '保护专工',
    estimatedDuration: 8,
    notes: '需要专业保护测试仪器'
  }
];

const seedWorkOrders = async () => {
  try {
    await connectDB();

    // 清空现有工单数据
    await WorkOrder.deleteMany({});
    console.log('已清空现有工单数据');

    // 获取所有电站
    const stations = await PowerStation.find({});
    if (stations.length === 0) {
      console.error('没有找到电站数据，请先运行电站种子数据脚本');
      process.exit(1);
    }

    // 为每个工单随机分配电站
    const workOrdersWithStations = sampleWorkOrders.map(workOrder => ({
      ...workOrder,
      stationId: stations[Math.floor(Math.random() * stations.length)]._id
    }));

    // 插入工单数据
    const createdWorkOrders = await WorkOrder.insertMany(workOrdersWithStations);
    console.log(`✅ 成功创建 ${createdWorkOrders.length} 个工单`);

    // 显示统计信息
    const stats = await WorkOrder.aggregate([
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 }
        }
      }
    ]);

    console.log('\n📊 工单状态统计:');
    stats.forEach(stat => {
      const statusMap: { [key: string]: string } = {
        'OPEN': '待处理',
        'IN_PROGRESS': '进行中',
        'COMPLETED': '已完成',
        'CANCELLED': '已取消'
      };
      console.log(`  ${statusMap[stat._id] || stat._id}: ${stat.count} 个`);
    });

    console.log('\n🎉 工单种子数据创建完成！');
    process.exit(0);
  } catch (error) {
    console.error('❌ 创建工单种子数据失败:', error);
    process.exit(1);
  }
};

// 如果直接运行此脚本
if (require.main === module) {
  seedWorkOrders();
}

export default seedWorkOrders;
