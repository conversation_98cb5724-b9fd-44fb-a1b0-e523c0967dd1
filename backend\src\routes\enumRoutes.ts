import express from 'express';
import { getAllEnums, getEnumByType } from '../controllers/enumController';

const router = express.Router();

/**
 * @route GET /api/v1/enums
 * @desc 获取所有枚举值
 * @access Public
 */
router.get('/', getAllEnums);

/**
 * @route GET /api/v1/enums/:type
 * @desc 获取特定类型的枚举值
 * @param {string} type - 枚举类型 (employeeStatus, dispatchType, etc.)
 * @access Public
 */
router.get('/:type', getEnumByType);

export default router;