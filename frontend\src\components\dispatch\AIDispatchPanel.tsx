import React, { useState, useEffect } from 'react';
import { Card, Button, Input, Select, Tag, Avatar, Progress, message, Spin } from 'antd';
import { CloseOutlined, RobotOutlined, UserOutlined, CheckOutlined } from '@ant-design/icons';

const { TextArea } = Input;
const { Option } = Select;

interface AIDispatchPanelProps {
  onClose: () => void;
  employees: any[];
  stations: any[];
  onDispatchSelect: (employee: any) => void;
}

interface AIRecommendation {
  employee: any;
  matchScore: number;
  reasons: string[];
  availability: 'available' | 'busy' | 'unavailable';
}

const AIDispatchPanel: React.FC<AIDispatchPanelProps> = ({
  onClose,
  employees,
  stations,
  onDispatchSelect
}) => {
  const [requirements, setRequirements] = useState('');
  const [selectedPosition, setSelectedPosition] = useState('');
  const [selectedStation, setSelectedStation] = useState('');
  const [selectedEmployees, setSelectedEmployees] = useState<string[]>([]);
  const [recommendations, setRecommendations] = useState<AIRecommendation[]>([]);
  const [loading, setLoading] = useState(false);
  const [positions, setPositions] = useState<any[]>([]);

  // 获取岗位数据
  const fetchPositions = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/enums/position', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (response.ok) {
        const result = await response.json();
        setPositions(result.data || []);
      }
    } catch (error) {
      console.error('获取岗位数据失败:', error);
    }
  };

  // 获取电站数据
  const fetchStations = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/stations', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (response.ok) {
        const result = await response.json();
        setStations(result.data || []);
      }
    } catch (error) {
      console.error('获取电站数据失败:', error);
    }
  };

  useEffect(() => {
    fetchPositions();
    fetchStations();
  }, []);

  // 获取岗位选项
  const positionOptions = positions.length > 0 
    ? positions.map(pos => pos.label).filter(Boolean)
    : Array.from(new Set(employees.map(emp => emp.position).filter(Boolean)));

  // 生成AI推荐
  const generateRecommendations = async () => {
    if (!selectedPosition || !selectedStation) {
      message.warning('请选择岗位和电站');
      return;
    }

    setLoading(true);
    try {
      // 模拟AI推荐逻辑（实际应调用后端AI服务）
      const filteredEmployees = employees.filter(emp => 
        emp.position === selectedPosition && 
        emp.status === 'active'
      );

      const aiRecommendations: AIRecommendation[] = filteredEmployees
        .map(emp => {
          // 计算匹配度
          let matchScore = 60; // 基础分
          
          // 经验加分
          if (emp.experience >= 5) matchScore += 20;
          else if (emp.experience >= 3) matchScore += 10;
          
          // 调度历史加分
          const daysSinceLastScheduled = emp.lastScheduled 
            ? Math.floor((Date.now() - new Date(emp.lastScheduled).getTime()) / (1000 * 60 * 60 * 24))
            : 365;
          
          if (daysSinceLastScheduled > 180) matchScore += 15;
          else if (daysSinceLastScheduled > 90) matchScore += 10;
          
          // 技能匹配加分
          if (emp.skills && emp.skills.length > 0) matchScore += 10;
          
          // 证书加分
          if (emp.certificates && emp.certificates.length > 0) matchScore += 5;

          const reasons = [];
          if (emp.experience >= 5) reasons.push(`${emp.experience}年丰富经验`);
          if (daysSinceLastScheduled > 180) reasons.push('长期未调度，可优先安排');
          if (emp.skills && emp.skills.length > 0) reasons.push('技能匹配度高');
          if (emp.certificates && emp.certificates.length > 0) reasons.push('持有相关证书');

          return {
            employee: emp,
            matchScore: Math.min(matchScore, 95),
            reasons: reasons.length > 0 ? reasons : ['基础条件符合'],
            availability: daysSinceLastScheduled > 30 ? 'available' : 'busy'
          };
        })
        .sort((a, b) => b.matchScore - a.matchScore)
        .slice(0, 6);

      setRecommendations(aiRecommendations);
      
      if (aiRecommendations.length === 0) {
        message.info('未找到符合条件的员工推荐');
      } else {
        message.success(`生成了 ${aiRecommendations.length} 个推荐方案`);
      }
    } catch (error) {
      console.error('生成推荐失败:', error);
      message.error('生成推荐失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  // 切换员工选择状态
  const toggleEmployeeSelection = (employeeId: string) => {
    setSelectedEmployees(prev => 
      prev.includes(employeeId) 
        ? prev.filter(id => id !== employeeId)
        : [...prev, employeeId]
    );
  };

  // 确认调度
  const confirmDispatch = async () => {
    if (selectedEmployees.length === 0) {
      message.warning('请至少选择一名员工');
      return;
    }

    try {
      // 这里应该调用后端API进行实际调度
      message.success(`已选择 ${selectedEmployees.length} 名员工进行调度`);
      onClose();
    } catch (error) {
      message.error('调度失败，请重试');
    }
  };

  // 获取状态颜色
  const getStatusColor = (availability: string) => {
    switch (availability) {
      case 'available': return '#52c41a';
      case 'busy': return '#faad14';
      case 'unavailable': return '#ff4d4f';
      default: return '#d9d9d9';
    }
  };

  // 获取状态文本
  const getStatusText = (availability: string) => {
    switch (availability) {
      case 'available': return '可调度';
      case 'busy': return '繁忙';
      case 'unavailable': return '不可用';
      default: return '未知';
    }
  };



  return (
    <div className="fixed inset-0 z-50 flex">
      {/* 背景遮罩 */}
      <div className="flex-1 bg-black bg-opacity-50" onClick={onClose} />
      
      {/* 右侧面板 */}
      <div className="w-96 bg-slate-800 border-l border-gray-600 flex flex-col h-full">
        {/* 头部 */}
        <div className="flex items-center justify-between p-4 border-b border-gray-600">
          <div className="flex items-center space-x-2">
            <RobotOutlined className="text-cyan-400 text-lg" />
            <h3 className="text-white text-lg font-semibold">AI智能调度</h3>
          </div>
          <Button
            type="text"
            icon={<CloseOutlined />}
            onClick={onClose}
            className="text-gray-400 hover:text-white"
          />
        </div>

        {/* 内容区域 */}
        <div className="flex-1 overflow-y-auto p-4 space-y-4">
          {/* 需求描述 */}
          <Card title="调度需求" size="small" className="bg-slate-700 border-gray-600">
            <TextArea
              value={requirements}
              onChange={(e) => setRequirements(e.target.value)}
              placeholder="请描述具体的调度需求..."
              rows={3}
              className="bg-slate-600 border-gray-500 text-white"
            />
          </Card>

          {/* 岗位和电站选择 */}
          <Card title="基本信息" size="small" className="bg-slate-700 border-gray-600">
            <div className="space-y-3">
              <div>
                <label className="block text-gray-300 text-sm mb-1">目标岗位</label>
                <Select
                  value={selectedPosition}
                  onChange={setSelectedPosition}
                  placeholder="请选择岗位"
                  className="w-full"
                >
                  {positionOptions.map(position => (
                    <Option key={position} value={position}>{position}</Option>
                  ))}
                </Select>
              </div>
              
              <div>
                <label className="block text-gray-300 text-sm mb-1">目标电站</label>
                <Select
                  value={selectedStation}
                  onChange={setSelectedStation}
                  placeholder="请选择电站"
                  className="w-full"
                >
                  {stations.map(station => (
                    <Option key={station.id || station._id} value={station.id || station._id}>
                      {station.name}
                    </Option>
                  ))}
                </Select>
              </div>
            </div>
          </Card>

          {/* 生成推荐按钮 */}
          <Button
            type="primary"
            block
            icon={<RobotOutlined />}
            onClick={generateRecommendations}
            loading={loading}
            disabled={!selectedPosition || !selectedStation}
            className="bg-cyan-600 border-cyan-600 hover:bg-cyan-700"
          >
            生成AI推荐
          </Button>

          {/* AI推荐结果 */}
          {recommendations.length > 0 && (
            <Card 
              title={`AI推荐方案 (${recommendations.length}个)`} 
              size="small" 
              className="bg-slate-700 border-gray-600"
            >
              <div className="space-y-3">
                {recommendations.map((rec, index) => (
                  <div
                    key={rec.employee.id}
                    className={`p-3 rounded border cursor-pointer transition-all ${
                      selectedEmployees.includes(rec.employee.id)
                        ? 'border-cyan-400 bg-cyan-900 bg-opacity-30'
                        : 'border-gray-600 hover:border-gray-500'
                    }`}
                    onClick={() => toggleEmployeeSelection(rec.employee.id)}
                  >
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex items-center space-x-2">
                        <Avatar size="small" icon={<UserOutlined />} />
                        <div>
                          <div className="text-white font-medium">{rec.employee.name}</div>
                          <div className="text-gray-400 text-xs">{rec.employee.position}</div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Tag color={getStatusColor(rec.availability)}>
                          {getStatusText(rec.availability)}
                        </Tag>
                        {selectedEmployees.includes(rec.employee.id) && (
                          <CheckOutlined className="text-cyan-400" />
                        )}
                      </div>
                    </div>
                    
                    <div className="mb-2">
                      <div className="flex items-center justify-between mb-1">
                        <span className="text-gray-300 text-xs">匹配度</span>
                        <span className="text-cyan-400 text-xs">{rec.matchScore}%</span>
                      </div>
                      <Progress 
                        percent={rec.matchScore} 
                        showInfo={false} 
                        strokeColor="#06b6d4"
                        trailColor="#374151"
                        size="small"
                      />
                    </div>
                    
                    <div className="text-xs text-gray-400">
                      推荐理由: {rec.reasons.join(', ')}
                    </div>
                  </div>
                ))}
              </div>
            </Card>
          )}

          {/* 加载状态 */}
          {loading && (
            <div className="flex items-center justify-center py-8">
              <Spin size="large" />
              <span className="ml-2 text-gray-300">AI正在分析最佳调度方案...</span>
            </div>
          )}
        </div>

        {/* 底部操作 */}
        <div className="p-4 border-t border-gray-600">
          <div className="flex items-center justify-between mb-3">
            <span className="text-gray-300 text-sm">
              已选择 {selectedEmployees.length} 名员工
            </span>
          </div>
          <Button
            type="primary"
            block
            onClick={confirmDispatch}
            disabled={selectedEmployees.length === 0}
            className="bg-green-600 border-green-600 hover:bg-green-700"
          >
            确认调度安排
          </Button>
        </div>
      </div>
    </div>
  );
};

export default AIDispatchPanel;