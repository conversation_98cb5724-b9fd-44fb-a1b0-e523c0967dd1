
import { PowerStation, Employee, WorkOrder, User } from '../types';

// API基础配置
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3002/api/v1';

// 创建API客户端
class ApiClient {
  private baseURL: string;
  private token: string | null;
  private autoLoginPromise: Promise<void> | null = null;

  constructor(baseURL: string) {
    this.baseURL = baseURL;
    this.token = localStorage.getItem('auth_token');
    
    console.log('ApiClient 初始化:', {
      baseURL,
      hasToken: !!this.token,
      isDev: import.meta.env.DEV
    });
    
    // 在开发模式下自动登录
    if (import.meta.env.DEV && !this.token) {
      console.log('开发模式且无token，启动自动登录...');
      this.autoLoginPromise = this.autoLogin();
    }
  }

  private async autoLogin(): Promise<void> {
    try {
      console.log('开发模式：尝试自动登录...');
      const response = await this.login('admin', 'admin123');
      console.log('自动登录成功:', response.user?.name || response.user?.email || '管理员');
      console.log('Token已设置:', this.token ? '是' : '否');
    } catch (error) {
      console.warn('自动登录失败，但不影响其他功能:', error);
      // 不重新抛出错误，允许应用继续运行
    }
  }

  // 公共请求方法，供其他服务使用
  async request<T>(
    endpoint: string, 
    options: RequestInit = {}
  ): Promise<T> {
    // 如果有自动登录过程，等待其完成
    if (this.autoLoginPromise) {
      try {
        await this.autoLoginPromise;
      } catch (error) {
        console.error('等待自动登录失败:', error);
      }
      this.autoLoginPromise = null;
    }

    // 如果在开发模式下且没有token，尝试自动登录
    if (import.meta.env.DEV && !this.token && !this.autoLoginPromise) {
      console.log('请求时发现无token，启动自动登录...');
      this.autoLoginPromise = this.autoLogin();
      try {
        await this.autoLoginPromise;
      } catch (error) {
        console.error('请求时自动登录失败:', error);
      }
      this.autoLoginPromise = null;
    }

    const url = `${this.baseURL}${endpoint}`;
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      ...(options.headers as Record<string, string> || {}),
    };

    if (this.token) {
      headers.Authorization = `Bearer ${this.token}`;
    } else {
      console.warn(`请求 ${endpoint} 时没有token`);
    }

    try {
      const response = await fetch(url, {
        ...options,
        headers,
      });

      if (!response.ok) {
        if (response.status === 401 || response.status === 403) {
          console.warn(`认证失败 (${response.status})，清除token`);
          this.token = null;
          localStorage.removeItem('auth_token');
        }
        throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      // 对于调度相关的API，保留完整的响应结构
      if (endpoint.includes('/dispatch/')) {
        return data;
      }
      // 对于其他API，保持原有逻辑
      return data.data || data;
    } catch (error) {
      console.error(`API请求错误 [${endpoint}]:`, error);
      throw error;
    }
  }

  // 认证相关
  async login(username: string, password: string): Promise<{ user: User; token: string }> {
    // 直接发送登录请求，不使用request方法避免循环等待
    const url = `${this.baseURL}/auth/login`;
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ username, password }),
    });

    if (!response.ok) {
      throw new Error(`登录失败: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    const result = data.data || data;
    
    this.token = result.token;
    localStorage.setItem('auth_token', result.token);
    return result;
  }

  async logout(): Promise<void> {
    this.token = null;
    localStorage.removeItem('auth_token');
  }

  // 电站相关
  async getStations(): Promise<PowerStation[]> {
    return await this.request<PowerStation[]>('/stations');
  }

  async getStation(id: string): Promise<PowerStation> {
    return await this.request<PowerStation>(`/stations/${id}`);
  }

  async createStation(station: Omit<PowerStation, 'id'>): Promise<PowerStation> {
    return await this.request<PowerStation>('/stations', {
      method: 'POST',
      body: JSON.stringify(station),
    });
  }

  async updateStation(id: string, station: Partial<PowerStation>): Promise<PowerStation> {
    return await this.request<PowerStation>(`/stations/${id}`, {
      method: 'PUT',
      body: JSON.stringify(station),
    });
  }

  async deleteStation(id: string): Promise<void> {
    await this.request(`/stations/${id}`, { method: 'DELETE' });
  }

  // 员工相关
  async getEmployees(): Promise<Employee[]> {
    return await this.request<Employee[]>('/employees');
  }

  async getEmployee(id: string): Promise<Employee> {
    return await this.request<Employee>(`/employees/${id}`);
  }

  async createEmployee(employee: Omit<Employee, 'id'>): Promise<Employee> {
    return await this.request<Employee>('/employees', {
      method: 'POST',
      body: JSON.stringify(employee),
    });
  }

  async updateEmployee(id: string, employee: Partial<Employee>): Promise<Employee> {
    return await this.request<Employee>(`/employees/${id}`, {
      method: 'PUT',
      body: JSON.stringify(employee),
    });
  }

  async deleteEmployee(id: string): Promise<void> {
    await this.request(`/employees/${id}`, { method: 'DELETE' });
  }

  // 工单相关
  async getWorkOrders(): Promise<WorkOrder[]> {
    return await this.request<WorkOrder[]>('/work-orders');
  }

  async getWorkOrder(id: string): Promise<WorkOrder> {
    return await this.request<WorkOrder>(`/work-orders/${id}`);
  }

  async createWorkOrder(workOrder: Omit<WorkOrder, 'id'>): Promise<WorkOrder> {
    return await this.request<WorkOrder>('/work-orders', {
      method: 'POST',
      body: JSON.stringify(workOrder),
    });
  }

  async updateWorkOrder(id: string, workOrder: Partial<WorkOrder>): Promise<WorkOrder> {
    return await this.request<WorkOrder>(`/work-orders/${id}`, {
      method: 'PUT',
      body: JSON.stringify(workOrder),
    });
  }

  async deleteWorkOrder(id: string): Promise<void> {
    await this.request(`/work-orders/${id}`, { method: 'DELETE' });
  }

  // 发电量数据相关
  async getPowerGeneration(stationId?: string, period?: 'day' | 'month' | 'year'): Promise<any[]> {
    const params = new URLSearchParams();
    if (stationId) params.append('stationId', stationId);
    if (period) params.append('period', period);
    
    return await this.request<any[]>(`/power-generation?${params.toString()}`);
  }

  // 统计数据
  async getStatistics(): Promise<{
    totalStations: number;
    totalEmployees: number;
    totalPowerGeneration: number;
    activeWorkOrders: number;
  }> {
    return await this.request<any>('/statistics');
  }

  // 岗位相关
  async getAllPositions(): Promise<any[]> {
    return await this.request<any[]>('/positions/templates');
  }

  async getStationPositions(stationId: string): Promise<any[]> {
    return await this.request<any[]>(`/positions/station/${stationId}`);
  }

  async getEmployeePositions(employeeId: string): Promise<{ main: any[], concurrent: any[] }> {
    return await this.request<{ main: any[], concurrent: any[] }>(`/employees/${employeeId}/positions`);
  }

  async getStationPositionStats(stationId: string): Promise<{
    total: number;
    occupied: number;
    vacant: number;
    notConfigured: number;
    occupancyRate: string;
  }> {
    return await this.request<any>(`/positions/station/${stationId}/stats`);
  }

  // 管理页面数据
  async getManagementData(): Promise<{
    stations: any[];
    employees: any[];
    workOrders: any[];
    positions: any[];
    employeePositions: Record<string, any>;
    dispatchRecords: any[];
    summary: any;
  }> {
    return await this.request<any>('/management/data');
  }
}

// 创建API客户端实例
const apiClient = new ApiClient(API_BASE_URL);

// 导出便捷方法（保持向后兼容）
export const getStations = () => apiClient.getStations();
export const getEmployees = () => apiClient.getEmployees();
export const getWorkOrders = () => apiClient.getWorkOrders();

// 导出API客户端
export { apiClient };
export default apiClient;
