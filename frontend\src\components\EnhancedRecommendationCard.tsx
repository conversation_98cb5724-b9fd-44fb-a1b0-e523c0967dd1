import React from 'react';
import { Card, Row, Col, Tag, Progress, Tooltip, Button, Space, Divider, Typography } from 'antd';
import { 
  UserOutlined, 
  StarOutlined, 
  WarningOutlined, 
  ClockCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  BulbOutlined,
  TrophyOutlined
} from '@ant-design/icons';
import { EnhancedRecommendation } from '../services/dispatchService';

const { Text, Title } = Typography;

interface EnhancedRecommendationCardProps {
  recommendation: EnhancedRecommendation;
  onSelect?: (recommendation: EnhancedRecommendation) => void;
  onViewDetails?: (recommendation: EnhancedRecommendation) => void;
  onConfirm?: (recommendation: EnhancedRecommendation) => Promise<void>;
}

const EnhancedRecommendationCard: React.FC<EnhancedRecommendationCardProps> = ({
  recommendation,
  onSelect,
  onViewDetails,
  onConfirm
}) => {
  const { employee, businessStatus, overallScore, riskScore } = recommendation;

  // 获取评分颜色
  const getScoreColor = (score: number) => {
    if (score >= 80) return '#52c41a';
    if (score >= 60) return '#faad14';
    if (score >= 40) return '#fa8c16';
    return '#f5222d';
  };

  // 获取风险等级颜色
  const getRiskColor = (risk: number) => {
    if (risk < 30) return 'success';
    if (risk < 60) return 'warning';
    return 'error';
  };

  // 获取可用性状态
  const getAvailabilityStatus = () => {
    if (businessStatus.isAvailable) {
      return <Tag color="green" icon={<CheckCircleOutlined />}>可用</Tag>;
    } else {
      return (
        <Tooltip title={businessStatus.unavailableReason}>
          <Tag color="red" icon={<ExclamationCircleOutlined />}>不可用</Tag>
        </Tooltip>
      );
    }
  };

  return (
    <Card
      size="small"
      style={{ marginBottom: 16 }}
      title={
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Space>
            <UserOutlined />
            <span>{employee.name}</span>
            <Text type="secondary">({employee.position})</Text>
            {getAvailabilityStatus()}
          </Space>
          <Space>
            <Tag color={getScoreColor(overallScore) === '#52c41a' ? 'green' : 
                       getScoreColor(overallScore) === '#faad14' ? 'orange' : 
                       getScoreColor(overallScore) === '#fa8c16' ? 'volcano' : 'red'}>
              综合评分: {overallScore}
            </Tag>
          </Space>
        </div>
      }
      extra={
        <Space>
          {onViewDetails && (
            <Button size="small" onClick={() => onViewDetails(recommendation)}>
              详情
            </Button>
          )}
          {onSelect && (
            <Button 
              type="primary" 
              size="small" 
              disabled={!businessStatus.isAvailable}
              onClick={() => onSelect(recommendation)}
            >
              选择调度
            </Button>
          )}
          {onConfirm && (
            <Button 
              type="primary" 
              size="small" 
              disabled={!businessStatus.isAvailable}
              onClick={() => onConfirm(recommendation)}
            >
              确认调度
            </Button>
          )}
        </Space>
      }
    >
      {/* 基本信息 */}
      <Row gutter={16} style={{ marginBottom: 12 }}>
        <Col span={8}>
          <Text strong>部门:</Text> {employee.department}
        </Col>
        <Col span={8}>
          <Text strong>员工类型:</Text> {employee.employeeType}
        </Col>
        <Col span={8}>
          <Text strong>当前电站:</Text> {employee.currentStation?.name || '未知'}
        </Col>
      </Row>

      {/* 评分详情 */}
      <Row gutter={16} style={{ marginBottom: 12 }}>
        <Col span={6}>
          <div style={{ textAlign: 'center' }}>
            <Progress
              type="circle"
              size={60}
              percent={recommendation.skillMatchScore}
              strokeColor={getScoreColor(recommendation.skillMatchScore)}
              format={() => recommendation.skillMatchScore}
            />
            <div style={{ marginTop: 4, fontSize: 12 }}>技能匹配</div>
          </div>
        </Col>
        <Col span={6}>
          <div style={{ textAlign: 'center' }}>
            <Progress
              type="circle"
              size={60}
              percent={recommendation.experienceScore}
              strokeColor={getScoreColor(recommendation.experienceScore)}
              format={() => recommendation.experienceScore}
            />
            <div style={{ marginTop: 4, fontSize: 12 }}>经验评分</div>
          </div>
        </Col>
        <Col span={6}>
          <div style={{ textAlign: 'center' }}>
            <Progress
              type="circle"
              size={60}
              percent={recommendation.availabilityScore}
              strokeColor={getScoreColor(recommendation.availabilityScore)}
              format={() => recommendation.availabilityScore}
            />
            <div style={{ marginTop: 4, fontSize: 12 }}>可用性</div>
          </div>
        </Col>
        <Col span={6}>
          <div style={{ textAlign: 'center' }}>
            <Progress
              type="circle"
              size={60}
              percent={100 - riskScore}
              strokeColor={riskScore < 30 ? '#52c41a' : riskScore < 60 ? '#faad14' : '#f5222d'}
              format={() => `${100 - riskScore}`}
            />
            <div style={{ marginTop: 4, fontSize: 12 }}>安全评分</div>
          </div>
        </Col>
      </Row>

      {/* 技能匹配 */}
      {recommendation.matchedSkills.length > 0 && (
        <div style={{ marginBottom: 8 }}>
          <Text strong>匹配技能: </Text>
          {recommendation.matchedSkills.map(skill => (
            <Tag key={skill} color="green" icon={<CheckCircleOutlined />}>
              {skill}
            </Tag>
          ))}
        </div>
      )}

      {recommendation.missingSkills.length > 0 && (
        <div style={{ marginBottom: 8 }}>
          <Text strong>缺失技能: </Text>
          {recommendation.missingSkills.map(skill => (
            <Tag key={skill} color="red" icon={<ExclamationCircleOutlined />}>
              {skill}
            </Tag>
          ))}
        </div>
      )}

      {/* 业务状态 */}
      <Row gutter={16} style={{ marginBottom: 8 }}>
        <Col span={8}>
          <Tooltip title="疲劳度评分，越高表示越疲劳">
            <Text>疲劳度: </Text>
            <Tag color={businessStatus.fatigueScore < 30 ? 'green' : 
                       businessStatus.fatigueScore < 60 ? 'orange' : 'red'}>
              {businessStatus.fatigueScore}/100
            </Tag>
          </Tooltip>
        </Col>
        <Col span={8}>
          <Text>连续调度: </Text>
          <Tag color={businessStatus.continuousDispatches < 2 ? 'green' : 
                     businessStatus.continuousDispatches < 4 ? 'orange' : 'red'}>
            {businessStatus.continuousDispatches}次
          </Tag>
        </Col>
        <Col span={8}>
          <Text>绩效评分: </Text>
          <Tag color={businessStatus.performanceRating >= 85 ? 'green' : 
                     businessStatus.performanceRating >= 70 ? 'orange' : 'red'}>
            {businessStatus.performanceRating}/100
          </Tag>
        </Col>
      </Row>

      {/* 优势和风险 */}
      <Row gutter={16}>
        <Col span={12}>
          {recommendation.strengthReasons.length > 0 && (
            <div>
              <Text strong style={{ color: '#52c41a' }}>
                <TrophyOutlined /> 优势:
              </Text>
              <ul style={{ margin: '4px 0', paddingLeft: 16, fontSize: 12 }}>
                {recommendation.strengthReasons.slice(0, 2).map((reason, index) => (
                  <li key={index}>{reason}</li>
                ))}
              </ul>
            </div>
          )}
        </Col>
        <Col span={12}>
          {recommendation.riskFactors.length > 0 && (
            <div>
              <Text strong style={{ color: '#fa8c16' }}>
                <WarningOutlined /> 风险:
              </Text>
              <ul style={{ margin: '4px 0', paddingLeft: 16, fontSize: 12 }}>
                {recommendation.riskFactors.slice(0, 2).map((risk, index) => (
                  <li key={index}>{risk}</li>
                ))}
              </ul>
            </div>
          )}
        </Col>
      </Row>

      {/* 学习机会 */}
      {recommendation.learningOpportunities.length > 0 && (
        <div style={{ marginTop: 8 }}>
          <Text strong style={{ color: '#1890ff' }}>
            <BulbOutlined /> 学习机会:
          </Text>
          <div style={{ fontSize: 12, marginTop: 4 }}>
            {recommendation.learningOpportunities.slice(0, 1).map((opportunity, index) => (
              <Tag key={index} color="blue">{opportunity}</Tag>
            ))}
          </div>
        </div>
      )}

      {/* 推荐信息 */}
      <Divider style={{ margin: '8px 0' }} />
      <Row gutter={16} style={{ fontSize: 12 }}>
        <Col span={12}>
          <Text type="secondary">
            <ClockCircleOutlined /> 建议时长: {recommendation.recommendedDuration}天
          </Text>
        </Col>
        <Col span={12}>
          <Text type="secondary">
            <StarOutlined /> 建议开始: {new Date(recommendation.suggestedStartDate).toLocaleDateString()}
          </Text>
        </Col>
      </Row>
    </Card>
  );
};

export default EnhancedRecommendationCard;