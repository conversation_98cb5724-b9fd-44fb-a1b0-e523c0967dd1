# 智能推荐功能使用说明

## 功能概述

智能推荐系统已经完成优化，现在可以提供更详细的推荐结果和理由，帮助用户更好地判断和调整AI的推荐。

## 主要改进

### 1. 增强版AI推荐接口 (`/api/v1/dispatch/enhanced-ai-recommendation`)

**请求参数：**
```json
{
  "query": "需要一名电工到B电站进行设备维护",
  "requiredSkills": ["电工", "设备维护"],
  "preferredExperience": 3,
  "duration": 7,
  "startDate": "2024-01-15",
  "urgencyLevel": "medium",
  "targetStationId": "station_id",
  "maxCandidates": 10
}
```

**返回结果：**
```json
{
  "success": true,
  "data": {
    "recommendedEmployees": [
      {
        "employee": {
          "id": "employee_id",
          "name": "张三",
          "position": "电工",
          "department": "维护部",
          "skills": ["电工", "设备维护", "安全管理"],
          "certifications": ["电工证", "安全员证"]
        },
        "scores": {
          "overall": 85,
          "skillMatch": 90,
          "experience": 80,
          "availability": 95,
          "risk": 15
        },
        "skillAnalysis": {
          "matchedSkills": ["电工", "设备维护"],
          "missingSkills": [],
          "skillMatchPercentage": 100
        },
        "recommendation": {
          "strengths": [
            "技能完全匹配",
            "经验丰富",
            "近期无调度安排"
          ],
          "risks": [
            "跨区域调度需要考虑交通安排"
          ],
          "alternativeOptions": [
            "可安排短期调度"
          ],
          "learningOpportunities": [
            "可学习新设备维护技术"
          ]
        },
        "dispatchPlan": {
          "recommendedDuration": 7,
          "suggestedStartDate": "2024-01-15T00:00:00.000Z",
          "estimatedEndDate": "2024-01-22T00:00:00.000Z"
        },
        "status": {
          "isAvailable": true,
          "fatigueLevel": 20,
          "recentDispatches": 1,
          "continuousDispatches": 0,
          "performanceRating": 85
        }
      }
    ],
    "summary": {
      "totalCandidates": 5,
      "averageScore": 78,
      "riskLevel": "low",
      "confidence": 0.85,
      "bestCandidate": "张三",
      "recommendationReason": "基于技能匹配和可用性分析..."
    },
    "analysis": {
      "requirement": "需要一名电工到B电站进行设备维护",
      "analysisText": "详细的分析报告...",
      "riskAssessment": "风险评估报告...",
      "alternatives": ["替代方案1", "替代方案2"]
    },
    "dispatchRecommendation": {
      "urgencyLevel": "medium",
      "recommendedAction": "建议执行调度",
      "keyConsiderations": [
        "共找到 5 名候选员工",
        "平均匹配度: 78%",
        "整体风险等级: low",
        "推荐置信度: 85%"
      ]
    }
  },
  "message": "AI智能推荐完成，找到 5 名候选员工"
}
```

### 2. 原版AI推荐接口优化 (`/api/v1/dispatch/ai-recommendations`)

**请求参数：**
```json
{
  "requirement": "需要一名安全员到C电站进行安全检查"
}
```

**返回结果：**
```json
{
  "success": true,
  "requirement": "需要一名安全员到C电站进行安全检查",
  "data": {
    "recommendedEmployees": [...],
    "analysis": {
      "requirement": "需要一名安全员到C电站进行安全检查",
      "analysisText": "基于需求分析...",
      "riskAssessment": "风险评估...",
      "recommendations": "推荐建议..."
    },
    "recommendationReason": {
      "mainReasons": [
        "基于需求的详细分析",
        "基于当前 50 名活跃员工的分析",
        "考虑了 10 个电站的配置情况"
      ],
      "riskFactors": ["风险因素1", "风险因素2"],
      "suggestions": ["建议1", "建议2"]
    },
    "dispatchSuggestion": {
      "totalCandidates": 3,
      "recommendedAction": "找到合适的候选员工，建议进一步评估",
      "nextSteps": [
        "审查推荐的员工资质",
        "评估调度对源电站的影响",
        "制定详细的调度计划",
        "获得相关部门审批"
      ]
    }
  },
  "message": "AI推荐完成，找到 3 名候选员工"
}
```

## 使用建议

### 1. 选择合适的接口
- **增强版接口**：适用于需要详细分析和评分的复杂调度需求
- **原版接口**：适用于快速获取基础推荐的简单需求

### 2. 理解推荐结果
- **综合评分**：考虑技能匹配、经验、可用性等多个维度
- **推荐理由**：包含优势分析、风险评估和替代方案
- **调度计划**：提供具体的时间安排建议

### 3. 风险评估
- **low**：建议执行调度
- **medium**：谨慎执行，注意风险控制
- **high**：建议重新评估或寻找替代方案
- **critical**：不建议执行，风险过高

### 4. 调整和优化
- 根据推荐理由调整需求描述
- 考虑替代方案和学习机会
- 结合实际情况做最终决策

## 测试方法

1. 访问前端应用：http://localhost:5173/
2. 进入调度管理页面
3. 使用智能推荐功能
4. 查看详细的推荐结果和理由
5. 根据推荐信息做出调度决策

## 技术特点

- **智能分析**：基于AI大模型的自然语言理解
- **多维评分**：技能、经验、可用性、风险等综合评估
- **详细理由**：提供推荐的具体原因和风险分析
- **灵活配置**：支持自定义推荐规则和参数
- **实时数据**：基于最新的员工和电站信息

## 后续优化方向

1. 增加历史调度成功率分析
2. 支持批量推荐和对比
3. 增加推荐结果的可视化展示
4. 优化AI模型的准确性和响应速度