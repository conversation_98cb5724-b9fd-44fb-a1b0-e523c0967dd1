import React, { useState, useEffect } from 'react';
import { getAllEnums } from '../services/enumService';

const TestManualDispatch: React.FC = () => {
  const [enumData, setEnumData] = useState<any>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadEnumData = async () => {
      try {
        console.log('🔄 开始加载枚举数据...');
        const data = await getAllEnums();
        console.log('✅ 枚举数据加载成功:', data);
        setEnumData(data);
        setError(null);
      } catch (error) {
        console.error('❌ 加载枚举数据失败:', error);
        setError(error instanceof Error ? error.message : '未知错误');
      } finally {
        setLoading(false);
      }
    };
    loadEnumData();
  }, []);

  if (loading) {
    return <div>正在加载枚举数据...</div>;
  }

  if (error) {
    return <div style={{color: 'red'}}>错误: {error}</div>;
  }

  return (
    <div style={{padding: '20px'}}>
      <h2>枚举数据测试</h2>
      
      <div style={{marginBottom: '20px'}}>
        <h3>职位选择测试</h3>
        <select style={{padding: '8px', border: '1px solid #ccc'}}>
          <option value="">选择职位</option>
          {enumData.position?.map((pos: any) => (
            <option key={pos.value} value={pos.value}>
              {pos.label}
            </option>
          )) || [
            <option key="engineer" value="engineer">工程师</option>,
            <option key="technician" value="technician">技术员</option>
          ]}
        </select>
      </div>

      <div style={{marginBottom: '20px'}}>
        <h3>部门选择测试</h3>
        <select style={{padding: '8px', border: '1px solid #ccc'}}>
          <option value="">选择部门</option>
          {enumData.department?.map((dept: any) => (
            <option key={dept.value} value={dept.value}>
              {dept.label}
            </option>
          )) || [
            <option key="operations" value="operations">运行部</option>,
            <option key="maintenance" value="maintenance">检修部</option>
          ]}
        </select>
      </div>

      <div style={{marginBottom: '20px'}}>
        <h3>输入框测试</h3>
        <input 
          type="text" 
          placeholder="测试输入框" 
          style={{padding: '8px', border: '1px solid #ccc'}}
        />
      </div>

      <div style={{marginBottom: '20px'}}>
        <h3>枚举数据详情</h3>
        <pre style={{background: '#f5f5f5', padding: '10px', overflow: 'auto'}}>
          {JSON.stringify(enumData, null, 2)}
        </pre>
      </div>
    </div>
  );
};

export default TestManualDispatch;