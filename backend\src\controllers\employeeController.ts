import { Request, Response } from 'express';
import { ApiResponse } from '../types/index';
import { Employee } from '../models/Employee';
import { StationPosition } from '../models/StationPosition';
import { ConcurrentPosition } from '../models/ConcurrentPosition';
import { logger } from '../utils/logger';

// 获取所有员工
export const getAllEmployees = async (req: Request, res: Response): Promise<void> => {
  try {
    // 获取员工基本信息
    const employees = await Employee.find()
      .populate('homeStationId', 'name')
      .populate('currentStationId', 'name')
      .lean(); // 使用lean()提高性能

    // 为每个员工添加岗位信息
    const employeesWithPositions = await Promise.all(
      employees.map(async (employee) => {
        try {
          // 获取员工的主要岗位
          const mainPositions = await StationPosition.find({ employeeId: employee._id })
            .populate('positionId', 'positionName department level')
            .populate('stationId', 'name')
            .lean();

          // 获取员工的兼职岗位
          const concurrentPositions = await ConcurrentPosition.find({ 
            employeeId: employee._id, 
            status: 'active' 
          })
            .populate('positionId', 'positionName department level')
            .populate('stationId', 'name')
            .lean();

          // 确定主要岗位名称
          let primaryPositionName = '未分配';
          if (mainPositions.length > 0) {
            const primaryPosition = mainPositions[0];
            if (primaryPosition.positionId && typeof primaryPosition.positionId === 'object') {
              primaryPositionName = (primaryPosition.positionId as any).positionName || '未分配';
            }
          }

          return {
            ...employee,
            position: primaryPositionName,
            positions: {
              main: mainPositions,
              concurrent: concurrentPositions
            }
          };
        } catch (error) {
          logger.error(`获取员工 ${employee._id} 岗位信息失败:`, error);
          return {
            ...employee,
            position: '未分配',
            positions: {
              main: [],
              concurrent: []
            }
          };
        }
      })
    );
    
    const response: ApiResponse<typeof employeesWithPositions> = {
      success: true,
      data: employeesWithPositions
    };
    
    res.status(200).json(response);
  } catch (error) {
    logger.error('获取员工列表失败:', error);
    const response: ApiResponse = {
      success: false,
      message: '获取员工列表失败'
    };
    res.status(500).json(response);
  }
};

// 获取员工的岗位信息
export const getEmployeePositions = async (req: Request, res: Response): Promise<void> => {
  try {
    const { employeeId } = req.params;
    
    // 获取员工的主要岗位
    const mainPositions = await StationPosition.find({ employeeId })
      .populate('stationId', 'name')
      .populate('positionId', 'positionName department level');
    
    // 获取员工的兼职岗位
    const concurrentPositions = await ConcurrentPosition.find({ 
      employeeId, 
      status: 'active' 
    })
      .populate('stationId', 'name')
      .populate('positionId', 'positionName department level');
    
    const response: ApiResponse<{ main: typeof mainPositions, concurrent: typeof concurrentPositions }> = {
      success: true,
      data: {
        main: mainPositions,
        concurrent: concurrentPositions
      }
    };
    
    res.status(200).json(response);
  } catch (error) {
    logger.error('获取员工岗位信息失败:', error);
    const response: ApiResponse = {
      success: false,
      message: '获取员工岗位信息失败'
    };
    res.status(500).json(response);
  }
};

// 获取员工的工作历史
export const getEmployeeWorkHistory = async (req: Request, res: Response): Promise<void> => {
  try {
    const { employeeId } = req.params;
    
    // 获取历史岗位分配记录
    const historyPositions = await StationPosition.find({
      $or: [
        { employeeId },
        { 'assignmentHistory.employeeId': employeeId }
      ]
    })
      .populate('stationId', 'name')
      .populate('positionId', 'positionName department level')
      .populate('assignmentHistory.employeeId', 'name');
    
    // 获取历史兼职记录
    const historyConcurrent = await ConcurrentPosition.find({ employeeId })
      .populate('stationId', 'name')
      .populate('positionId', 'positionName department level');
    
    const response: ApiResponse<{ positions: typeof historyPositions, concurrent: typeof historyConcurrent }> = {
      success: true,
      data: {
        positions: historyPositions,
        concurrent: historyConcurrent
      }
    };
    
    res.status(200).json(response);
  } catch (error) {
    logger.error('获取员工工作历史失败:', error);
    const response: ApiResponse = {
      success: false,
      message: '获取员工工作历史失败'
    };
    res.status(500).json(response);
  }
};

// 根据员工类型获取员工
export const getEmployeesByType = async (req: Request, res: Response): Promise<void> => {
  try {
    const { type } = req.params;
    const employees = await Employee.find({ employeeType: type })
      .populate('homeStationId', 'name')
      .populate('currentStationId', 'name');
    
    const response: ApiResponse<typeof employees> = {
      success: true,
      data: employees
    };
    
    res.status(200).json(response);
  } catch (error) {
    logger.error('根据类型获取员工失败:', error);
    const response: ApiResponse = {
      success: false,
      message: '根据类型获取员工失败'
    };
    res.status(500).json(response);
  }
};

// 获取员工统计信息
export const getEmployeeStats = async (req: Request, res: Response): Promise<void> => {
  try {
    const { stationId } = req.query;
    
    let matchCondition = {};
    if (stationId) {
      matchCondition = { currentStationId: stationId };
    }
    
    const stats = await Employee.aggregate([
      { $match: matchCondition },
      {
        $group: {
          _id: '$employeeType',
          count: { $sum: 1 }
        }
      }
    ]);
    
    const totalCount = await Employee.countDocuments(matchCondition);
    
    const response: ApiResponse<{ total: number, byType: typeof stats }> = {
      success: true,
      data: {
        total: totalCount,
        byType: stats
      }
    };
    
    res.status(200).json(response);
  } catch (error) {
    logger.error('获取员工统计信息失败:', error);
    const response: ApiResponse = {
      success: false,
      message: '获取员工统计信息失败'
    };
    res.status(500).json(response);
  }
};

// 根据ID获取员工
export const getEmployeeById = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const employee = await Employee.findById(id)
      .populate('homeStationId', 'name')
      .populate('currentStationId', 'name');
    
    if (!employee) {
      const response: ApiResponse = {
        success: false,
        message: '员工不存在'
      };
      res.status(404).json(response);
      return;
    }
    
    const response: ApiResponse<typeof employee> = {
      success: true,
      data: employee
    };
    
    res.status(200).json(response);
  } catch (error) {
    logger.error('获取员工信息失败:', error);
    const response: ApiResponse = {
      success: false,
      message: '获取员工信息失败'
    };
    res.status(500).json(response);
  }
};

// 创建员工
export const createEmployee = async (req: Request, res: Response): Promise<void> => {
  try {
    const employeeData = req.body;
    
    // 如果没有指定currentStationId，默认设置为homeStationId
    if (!employeeData.currentStationId && employeeData.homeStationId) {
      employeeData.currentStationId = employeeData.homeStationId;
    }
    
    // 向后兼容：如果使用旧的stationId字段
    if (employeeData.stationId && !employeeData.homeStationId) {
      employeeData.homeStationId = employeeData.stationId;
      employeeData.currentStationId = employeeData.stationId;
    }
    
    const employee = new Employee(employeeData);
    await employee.save();
    
    const populatedEmployee = await Employee.findById(employee._id)
      .populate('homeStationId', 'name')
      .populate('currentStationId', 'name');
    
    const response: ApiResponse<typeof populatedEmployee> = {
      success: true,
      message: '员工创建成功',
      data: populatedEmployee
    };
    
    res.status(201).json(response);
  } catch (error) {
    logger.error('创建员工失败:', error);
    const response: ApiResponse = {
      success: false,
      message: '创建员工失败'
    };
    res.status(500).json(response);
  }
};

// 更新员工
export const updateEmployee = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const updateData = req.body;
    
    const employee = await Employee.findByIdAndUpdate(
      id,
      { ...updateData, updatedAt: new Date() },
      { new: true, runValidators: true }
    ).populate('homeStationId', 'name')
     .populate('currentStationId', 'name');
    
    if (!employee) {
      const response: ApiResponse = {
        success: false,
        message: '员工不存在'
      };
      res.status(404).json(response);
      return;
    }
    
    const response: ApiResponse<typeof employee> = {
      success: true,
      message: '员工信息更新成功',
      data: employee
    };
    
    res.status(200).json(response);
  } catch (error) {
    logger.error('更新员工信息失败:', error);
    const response: ApiResponse = {
      success: false,
      message: '更新员工信息失败'
    };
    res.status(500).json(response);
  }
};

// 删除员工
export const deleteEmployee = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    
    // 逻辑删除：将员工状态改为inactive而不是物理删除
    const employee = await Employee.findByIdAndUpdate(
      id,
      { 
        status: 'inactive',
        notes: `员工于${new Date().toISOString().split('T')[0]}离职`
      },
      { new: true }
    );
    
    if (!employee) {
      const response: ApiResponse = {
        success: false,
        message: '员工不存在'
      };
      res.status(404).json(response);
      return;
    }
    
    const response: ApiResponse = {
      success: true,
      message: '员工已标记为离职状态',
      data: employee
    };
    
    res.status(200).json(response);
  } catch (error) {
    logger.error('删除员工失败:', error);
    const response: ApiResponse = {
      success: false,
      message: '删除员工失败'
    };
    res.status(500).json(response);
  }
};

// 根据电站ID获取员工（向后兼容，返回当前在该电站的员工）
export const getEmployeesByStation = async (req: Request, res: Response): Promise<void> => {
  try {
    const { stationId } = req.params;
    const employees = await Employee.find({ currentStationId: stationId })
      .populate('homeStationId', 'name')
      .populate('currentStationId', 'name');
    
    const response: ApiResponse<typeof employees> = {
      success: true,
      data: employees
    };
    
    res.status(200).json(response);
  } catch (error) {
    logger.error('获取电站员工失败:', error);
    const response: ApiResponse = {
      success: false,
      message: '获取电站员工失败'
    };
    res.status(500).json(response);
  }
};

// 根据电站ID获取当前在该电站工作的员工
export const getCurrentEmployeesByStation = async (req: Request, res: Response): Promise<void> => {
  try {
    const { stationId } = req.params;
    const employees = await Employee.find({ currentStationId: stationId })
      .populate('homeStationId', 'name')
      .populate('currentStationId', 'name');
    
    const response: ApiResponse<typeof employees> = {
      success: true,
      data: employees
    };
    
    res.status(200).json(response);
  } catch (error) {
    logger.error('获取当前电站员工失败:', error);
    const response: ApiResponse = {
      success: false,
      message: '获取当前电站员工失败'
    };
    res.status(500).json(response);
  }
};

// 根据电站ID获取所属该电站的员工
export const getHomeEmployeesByStation = async (req: Request, res: Response): Promise<void> => {
  try {
    const { stationId } = req.params;
    const employees = await Employee.find({ homeStationId: stationId })
      .populate('homeStationId', 'name')
      .populate('currentStationId', 'name');
    
    const response: ApiResponse<typeof employees> = {
      success: true,
      data: employees
    };
    
    res.status(200).json(response);
  } catch (error) {
    logger.error('获取所属电站员工失败:', error);
    const response: ApiResponse = {
      success: false,
      message: '获取所属电站员工失败'
    };
    res.status(500).json(response);
  }
};

// 获取调入该电站的员工（当前在该电站但不属于该电站）
export const getDispatchedInEmployees = async (req: Request, res: Response): Promise<void> => {
  try {
    const { stationId } = req.params;
    const employees = await Employee.find({ 
      currentStationId: stationId,
      $expr: { $ne: ['$homeStationId', '$currentStationId'] }
    })
      .populate('homeStationId', 'name')
      .populate('currentStationId', 'name');
    
    const response: ApiResponse<typeof employees> = {
      success: true,
      data: employees
    };
    
    res.status(200).json(response);
  } catch (error) {
    logger.error('获取调入员工失败:', error);
    const response: ApiResponse = {
      success: false,
      message: '获取调入员工失败'
    };
    res.status(500).json(response);
  }
};

// 获取从该电站调出的员工（属于该电站但当前不在该电站）
export const getDispatchedOutEmployees = async (req: Request, res: Response): Promise<void> => {
  try {
    const { stationId } = req.params;
    const employees = await Employee.find({ 
      homeStationId: stationId,
      $expr: { $ne: ['$homeStationId', '$currentStationId'] }
    })
      .populate('homeStationId', 'name')
      .populate('currentStationId', 'name');
    
    const response: ApiResponse<typeof employees> = {
      success: true,
      data: employees
    };
    
    res.status(200).json(response);
  } catch (error) {
    logger.error('获取调出员工失败:', error);
    const response: ApiResponse = {
      success: false,
      message: '获取调出员工失败'
    };
    res.status(500).json(response);
  }
};

// 获取电站的员工调度概览
export const getStationDispatchOverview = async (req: Request, res: Response): Promise<void> => {
  try {
    const { stationId } = req.params;
    
    // 当前在该电站的员工
    const currentEmployees = await Employee.find({ currentStationId: stationId })
      .populate('homeStationId', 'name')
      .populate('currentStationId', 'name');
    
    // 所属该电站的员工
    const homeEmployees = await Employee.find({ homeStationId: stationId })
      .populate('homeStationId', 'name')
      .populate('currentStationId', 'name');
    
    // 调入的员工（当前在该电站但不属于该电站）
    const dispatchedIn = currentEmployees.filter(emp => 
      emp.homeStationId._id.toString() !== stationId
    );
    
    // 调出的员工（属于该电站但当前不在该电站）
    const dispatchedOut = homeEmployees.filter(emp => 
      emp.currentStationId._id.toString() !== stationId
    );
    
    // 在岗员工（属于该电站且当前在该电站）
    const stationed = homeEmployees.filter(emp => 
      emp.currentStationId._id.toString() === stationId
    );
    
    const overview = {
      stationId,
      total: {
        current: currentEmployees.length,
        home: homeEmployees.length,
        stationed: stationed.length,
        dispatchedIn: dispatchedIn.length,
        dispatchedOut: dispatchedOut.length
      },
      employees: {
        current: currentEmployees,
        home: homeEmployees,
        stationed,
        dispatchedIn,
        dispatchedOut
      }
    };
    
    const response: ApiResponse<typeof overview> = {
      success: true,
      data: overview
    };
    
    res.status(200).json(response);
  } catch (error) {
    logger.error('获取电站调度概览失败:', error);
    const response: ApiResponse = {
      success: false,
      message: '获取电站调度概览失败'
    };
    res.status(500).json(response);
  }
};