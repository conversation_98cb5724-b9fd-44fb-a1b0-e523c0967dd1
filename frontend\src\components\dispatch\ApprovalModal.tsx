import React, { useState } from 'react';
import { DispatchRecord } from '../../services/dispatchService';
import { Icons } from '../../constants/index';

interface ApprovalModalProps {
  isOpen: boolean;
  record: DispatchRecord | null;
  onClose: () => void;
  onApprove: (recordId: string, approved: boolean, comment: string) => void;
}

const ApprovalModal: React.FC<ApprovalModalProps> = ({ 
  isOpen, 
  record, 
  onClose, 
  onApprove 
}) => {
  const [comment, setComment] = useState('');
  const [decision, setDecision] = useState<'approve' | 'reject' | null>(null);

  if (!isOpen || !record) return null;

  const handleSubmit = () => {
    if (decision === null) {
      alert('请选择审核结果');
      return;
    }
    
    onApprove(record.id, decision === 'approve', comment);
    setComment('');
    setDecision(null);
    onClose();
  };

  const handleClose = () => {
    setComment('');
    setDecision(null);
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold">审核调度申请</h3>
          <button
            onClick={handleClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <Icons.X className="w-5 h-5" />
          </button>
        </div>

        {/* 申请详情 */}
        <div className="mb-6 space-y-3">
          <div className="flex justify-between">
            <span className="text-gray-600">员工姓名:</span>
            <span className="font-medium">{record.employeeName}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">调度类型:</span>
            <span className="font-medium">{record.dispatchType}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">目标电站:</span>
            <span className="font-medium">{record.toStation}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">申请时间:</span>
            <span className="font-medium">
              {new Date(record.requestDate).toLocaleString()}
            </span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">来源:</span>
            <span className="font-medium">
              {record.source === 'ai' ? 'AI推荐' : '手工创建'}
            </span>
          </div>
        </div>

        {/* 审核决定 */}
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            审核结果
          </label>
          <div className="flex space-x-4">
            <label className="flex items-center">
              <input
                type="radio"
                name="decision"
                value="approve"
                checked={decision === 'approve'}
                onChange={(e) => setDecision(e.target.value as 'approve')}
                className="mr-2"
              />
              <span className="text-green-600">通过</span>
            </label>
            <label className="flex items-center">
              <input
                type="radio"
                name="decision"
                value="reject"
                checked={decision === 'reject'}
                onChange={(e) => setDecision(e.target.value as 'reject')}
                className="mr-2"
              />
              <span className="text-red-600">拒绝</span>
            </label>
          </div>
        </div>

        {/* 审核意见 */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            审核意见
          </label>
          <textarea
            value={comment}
            onChange={(e) => setComment(e.target.value)}
            placeholder="请输入审核意见..."
            rows={3}
            className="w-full border border-gray-300 rounded-lg px-3 py-2"
          />
        </div>

        {/* 操作按钮 */}
        <div className="flex justify-end space-x-3">
          <button
            onClick={handleClose}
            className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50"
          >
            取消
          </button>
          <button
            onClick={handleSubmit}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            提交审核
          </button>
        </div>
      </div>
    </div>
  );
};

export default ApprovalModal;