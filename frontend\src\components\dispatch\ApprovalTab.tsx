import React from 'react';
import { DispatchRecord } from '../../services/dispatchService';
import { Icons } from '../../constants/index';

interface ApprovalTabProps {
  dispatchRecords: DispatchRecord[];
  onApprove: (record: DispatchRecord) => void;
}

const ApprovalTab: React.FC<ApprovalTabProps> = ({ dispatchRecords, onApprove }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'text-yellow-600 bg-yellow-100';
      case 'approved': return 'text-green-600 bg-green-100';
      case 'rejected': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending': return '待审核';
      case 'approved': return '已通过';
      case 'rejected': return '已拒绝';
      default: return status;
    }
  };

  const getTypeText = (type: string) => {
    switch (type) {
      case 'temporary': return '临时调度';
      case 'permanent': return '永久调度';
      case 'emergency': return '紧急调度';
      default: return type;
    }
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow">
      <h3 className="text-lg font-semibold mb-6 flex items-center">
        <Icons.Eye className="w-5 h-5 mr-2" />
        申请审批
      </h3>

      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                员工姓名
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                调度类型
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                目标电站
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                申请时间
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                状态
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                来源
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                操作
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {dispatchRecords.map((record) => (
              <tr key={record.id} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  {record.employeeName}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {getTypeText(record.dispatchType)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {record.targetStation}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {new Date(record.requestDate).toLocaleDateString()}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(record.status)}`}>
                    {getStatusText(record.status)}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {record.source === 'ai' ? 'AI推荐' : '手工创建'}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  {record.status === 'pending' && (
                    <button
                      onClick={() => onApprove(record)}
                      className="text-blue-600 hover:text-blue-900"
                    >
                      审核
                    </button>
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
        
        {dispatchRecords.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            暂无调度申请记录
          </div>
        )}
      </div>
    </div>
  );
};

export default ApprovalTab;