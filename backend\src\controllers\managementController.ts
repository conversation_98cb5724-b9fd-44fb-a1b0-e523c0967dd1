import { Request, Response } from 'express';
import { ApiResponse } from '../types/index';
import { Employee, PowerStation, Position, StationPosition, ConcurrentPosition, DispatchRecord } from '../models';
import WorkOrder from '../models/WorkOrder';
import { logger } from '../utils/logger';

// 获取管理页面所需的所有数据 - 触发文件变化
export const getManagementData = async (req: Request, res: Response): Promise<void> => {
  try {
    logger.info('开始获取管理页面数据...');

    // 并行获取基础数据
    const [stations, employees, workOrders, positions, dispatchRecords] = await Promise.all([
      PowerStation.find().lean(),
      Employee.find()
        .populate('homeStationId', 'name')
        .populate('currentStationId', 'name')
        .lean(),
      WorkOrder.find()
        .populate('stationId', 'name')
        .populate('assignedTo', 'name')
        .lean(),
      Position.find().lean(),
      DispatchRecord.find({ status: { $in: ['active', 'approved'] } })
        .populate('employeeId', 'name')
        .populate('fromStationId', 'name')
        .populate('toStationId', 'name')
        .lean()
    ]);

    logger.info(`基础数据获取完成: 电站${stations.length}个, 员工${employees.length}人, 工单${workOrders.length}个, 岗位${positions.length}个, 调度记录${dispatchRecords.length}条`);

    // 获取所有员工的岗位信息
    const employeeIds = employees.map((emp: any) => emp._id);
    
    const [stationPositions, concurrentPositions] = await Promise.all([
      StationPosition.find({ employeeId: { $in: employeeIds } })
        .populate('stationId', 'name')
        .populate('positionId', 'positionName department level')
        .lean(),
      ConcurrentPosition.find({ 
        employeeId: { $in: employeeIds }, 
        status: 'active' 
      })
        .populate('stationId', 'name')
        .populate('positionId', 'positionName department level')
        .lean()
    ]);

    logger.info(`岗位数据获取完成: 主要岗位${stationPositions.length}个, 兼职岗位${concurrentPositions.length}个`);

    // 调试：检查岗位数据结构
    if (stationPositions.length > 0) {
      logger.info('岗位数据示例:', JSON.stringify(stationPositions[0], null, 2));
    }

    // 构建员工岗位映射
    const employeePositions: Record<string, {
      main: any[];
      concurrent: any[];
      primaryPosition?: string;
    }> = {};

    // 初始化所有员工的岗位信息
    employees.forEach((emp: any) => {
      const empId = emp._id.toString();
      employeePositions[empId] = {
        main: [],
        concurrent: [],
        primaryPosition: '未分配岗位'
      };
    });

    // 填充主要岗位
    let assignedCount = 0;
    stationPositions.forEach((sp: any) => {
      const empId = sp.employeeId.toString();
      if (employeePositions[empId]) {
        employeePositions[empId].main.push(sp);
        // 设置主要岗位名称
        if (sp.positionId && sp.positionId.positionName && (!employeePositions[empId].primaryPosition || employeePositions[empId].primaryPosition === '未分配岗位')) {
          employeePositions[empId].primaryPosition = sp.positionId.positionName;
          assignedCount++;
        }
      }
    });

    logger.info(`岗位分配完成: ${assignedCount}个员工被分配了岗位`);

    // 调试：检查分配结果
    const positionStats = Object.values(employeePositions).reduce((acc: any, ep: any) => {
      acc[ep.primaryPosition] = (acc[ep.primaryPosition] || 0) + 1;
      return acc;
    }, {});
    logger.info('岗位分布统计:', JSON.stringify(positionStats, null, 2));

    // 填充兼职岗位
    concurrentPositions.forEach((cp: any) => {
      const empId = cp.employeeId.toString();
      if (employeePositions[empId]) {
        employeePositions[empId].concurrent.push(cp);
      }
    });

    // 处理员工调度状态
    const processedEmployees = employees.map((emp: any) => {
      const empId = emp._id.toString();
      
      // 查找该员工的活跃调度记录
      const activeDispatch = dispatchRecords.find((record: any) =>
        record.employeeId && record.employeeId._id && 
        record.employeeId._id.toString() === empId &&
        (record.status === 'active' || record.status === 'approved')
      );

      // 判断调度状态：比较员工归属电站和当前电站
      const homeStationId = emp.homeStationId?._id?.toString() || emp.homeStationId?.toString();
      const currentStationId = emp.currentStationId?._id?.toString() || emp.currentStationId?.toString();
      const isDispatched = homeStationId !== currentStationId;

      // 添加虚拟字段
      const processedEmp = {
        ...emp,
        id: emp._id.toString(), // 添加id字段
        primaryPosition: employeePositions[empId]?.primaryPosition || '未分配岗位',
        isDispatched: isDispatched,
        dispatchRecord: activeDispatch || null,
        homeStationName: (emp.homeStationId as any)?.name || null,
        currentStationName: (emp.currentStationId as any)?.name || null,
        dispatchFromStation: isDispatched ? (emp.homeStationId as any)?.name || null : null,
        dispatchToStation: isDispatched ? (emp.currentStationId as any)?.name || null : null
      };

      return processedEmp;
    });

    // 构建响应数据
    const responseData = {
      stations: stations.map((station: any) => ({
        ...station,
        id: station._id.toString()
      })),
      employees: processedEmployees,
      workOrders: workOrders.map((wo: any) => ({
        ...wo,
        id: wo._id.toString()
      })),
      positions: positions.map((pos: any) => ({
        ...pos,
        id: pos._id.toString()
      })),
      employeePositions,
      dispatchRecords: dispatchRecords.map((dr: any) => ({
        ...dr,
        id: dr._id.toString()
      })),
      summary: {
        totalStations: stations.length,
        totalEmployees: employees.length,
        totalWorkOrders: workOrders.length,
        totalPositions: positions.length,
        activeDispatches: dispatchRecords.length,
        employeesWithPositions: Object.values(employeePositions).filter((ep: any) => ep.primaryPosition !== '未分配岗位').length
      }
    };

    logger.info('管理页面数据组装完成');

    const response: ApiResponse<typeof responseData> = {
      success: true,
      data: responseData
    };

    res.status(200).json(response);
  } catch (error) {
    logger.error('获取管理页面数据失败:', error);
    const response: ApiResponse = {
      success: false,
      message: '获取管理页面数据失败'
    };
    res.status(500).json(response);
  }
};