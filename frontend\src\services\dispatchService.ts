import { apiClient } from './api';

export interface DispatchRecord {
  id: string;
  employeeId: string;
  employeeName: string;
  fromStation: string;  // 这里保持不变，因为这是显示用的名称
  toStation: string;    // 这里保持不变，因为这是显示用的名称
  dispatchType: 'temporary' | 'permanent' | 'emergency';  // 修正：与后端保持一致
  status: 'pending' | 'approved' | 'rejected' | 'completed';
  source: 'manual' | 'ai_query' | 'ai_recommendation';
  requestDate: string;
  startDate: string;
  endDate?: string;
  reason: string;
  aiQuery?: string;
  riskAnalysis?: string;
  approvedBy?: string;
  approvedAt?: string;
  rejectionReason?: string;
}

export interface CreateDispatchRequest {
  employeeId: string;
  fromStationId: string;  // 修正：与后端保持一致
  toStationId: string;    // 修正：与后端保持一致
  dispatchType: 'temporary' | 'permanent' | 'emergency';  // 修正：与后端保持一致
  startDate: string;
  endDate?: string;
  reason: string;
  source?: 'manual' | 'ai_query' | 'ai_recommendation';
  aiQuery?: string;
}

export interface CreateBatchDispatchRequest {
  records: {
    employeeId: string;
    fromStationId: string;
    toStationId: string;
    dispatchType: string;
    startDate: string;
    endDate?: string;
  }[];
  reason: string;
  source?: 'manual' | 'ai_query' | 'ai_recommendation';
  aiQuery?: string;
}

export interface DispatchRecordsResponse {
  records: DispatchRecord[];
  pagination: {
    current: number;
    pageSize: number;
    total: number;
    pages: number;
  };
}

export interface DispatchStats {
  total: number;
  pending: number;
  approved: number;
  rejected: number;
  completed: number;
  byType: Record<string, number>;
  bySource: Record<string, number>;
}

export interface BatchDispatchRequest {
  employeeIds: string[];
  fromStationId?: string;  // 修正：与后端保持一致
  toStationId: string;     // 修正：与后端保持一致
  dispatchType: 'temporary' | 'permanent' | 'emergency';  // 修正：与后端保持一致
  startDate: string;
  endDate?: string;
  reason: string;
  source?: 'manual';
}

export interface AIQueryRequest {
  query: string;
}

export interface AIQueryResponse {
  employees: any[];
  riskAnalysis: string;
  sqlQuery?: string;
}

export interface AIRecommendationRequest {
  requirement: string;
}

export interface AIRecommendationResponse {
  recommendations: any[];
  riskAnalysis: string;
  reasoning?: string;
}

// 增强版AI推荐请求接口
export interface EnhancedAIRecommendationRequest {
  query: string;
  targetStationId?: string;
  urgencyLevel?: 'low' | 'medium' | 'high' | 'emergency';
  requiredSkills?: string[];
  preferredExperience?: number;
  duration?: number;
  startDate?: string;
  maxCandidates?: number;
  config?: Partial<RecommendationConfig>;
}

// 增强版AI推荐响应接口
export interface EnhancedAIRecommendationResponse {
  success: boolean;
  data: {
    // 推荐的员工列表（按评分排序）
    recommendedEmployees: {
      // 员工基本信息
      employee: {
        id: string;
        name: string;
        position: string;
        department: string;
        employeeType: string;
        skills: string[];
        certifications: string[];
        currentStation: any;
        homeStation: any;
      };
      
      // 推荐评分详情
      scores: {
        overall: number;
        skillMatch: number;
        experience: number;
        availability: number;
        risk: number;
      };
      
      // 技能匹配分析
      skillAnalysis: {
        matchedSkills: string[];
        missingSkills: string[];
        skillMatchPercentage: number;
      };
      
      // 推荐理由
      recommendation: {
        strengths: string[];
        risks: string[];
        alternativeOptions: string[];
        learningOpportunities: string[];
        skillDevelopment: string[];
      };
      
      // 调度计划建议
      dispatchPlan: {
        recommendedDuration: number;
        suggestedStartDate: Date;
        estimatedEndDate: Date;
      };
      
      // 员工状态
      status: {
        isAvailable: boolean;
        fatigueLevel: number;
        recentDispatches: number;
        continuousDispatches: number;
        performanceRating: number;
      };
    }[];
    
    // 推荐总结
    summary: {
      totalCandidates: number;
      averageScore: number;
      riskLevel: 'low' | 'medium' | 'high' | 'critical';
      confidence: number;
      bestCandidate: string | null;
      recommendationReason: string;
    };
    
    // 详细分析报告
    analysis: {
      requirement: string;
      analysisText: string;
      riskAssessment: string;
      alternatives: string[];
      configurationUsed: RecommendationConfig;
    };
    
    // 调度建议
    dispatchRecommendation: {
      urgencyLevel: string;
      recommendedAction: string;
      keyConsiderations: string[];
    };
  };
  message: string;
}

// 推荐配置接口
export interface RecommendationConfig {
  skillMatchWeight: number;
  experienceWeight: number;
  positionMatchWeight: number;
  locationWeight: number;
  maxContinuousDispatches: number;
  minRestDaysBetweenDispatches: number;
  fatigueThreshold: number;
  minStaffRemaining: number;
  criticalPositionProtection: string[];
  emergencyResponseTime: number;
  preferInternalTransfer: boolean;
  preferExperiencedStaff: boolean;
  considerTrainingNeeds: boolean;
  allowOvertimeDispatch: boolean;
  allowCrossRegionDispatch: boolean;
  requireManagerApproval: boolean;
}

// 增强版推荐结果接口
export interface EnhancedRecommendation {
  employeeId: string;
  employee: {
    _id: string;
    name: string;
    position: string;
    department: string;
    employeeType: string;
    status: string;
    skills: string[];
    certifications: string[];
    currentStation: any;
    homeStation: any;
  };
  businessStatus: {
    employeeId: string;
    totalDispatches: number;
    recentDispatches: number;
    continuousDispatches: number;
    lastDispatchEndDate?: Date;
    fatigueScore: number;
    workloadScore: number;
    performanceRating: number;
    adaptabilityScore: number;
    isAvailable: boolean;
    unavailableReason?: string;
  };
  overallScore: number;
  skillMatchScore: number;
  experienceScore: number;
  availabilityScore: number;
  riskScore: number;
  matchedSkills: string[];
  missingSkills: string[];
  strengthReasons: string[];
  riskFactors: string[];
  recommendedDuration: number;
  suggestedStartDate: Date;
  alternativeOptions: string[];
  learningOpportunities: string[];
  skillDevelopment: string[];
  reason?: string; // 推荐理由
  targetStation?: string; // 目标电站
  dispatchType?: string; // 调度类型
  startDate?: string; // 开始日期
  endDate?: string; // 结束日期
  priority?: string; // 优先级
}

export interface ApprovalRequest {
  approved: boolean;
  reason?: string;
}

export class DispatchService {
  constructor() {
    // 使用全局的 apiClient，不需要单独的 baseUrl
  }

  // 获取调度记录
  async getDispatchRecords(params?: {
    status?: string;
    fromStationId?: string;
    toStationId?: string;
    startDate?: string;
    endDate?: string;
    page?: number;
    limit?: number;
    source?: string;
  }): Promise<DispatchRecordsResponse> {
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          queryParams.append(key, value.toString());
        }
      });
    }

    const endpoint = `/dispatch/records${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    const response = await apiClient.request(endpoint, { method: 'GET' }) as any;

    // 转换后端数据结构为前端期望的格式
    if (response?.records) {
      const transformedRecords = response.records.map((record: any) => ({
        id: record.id || record._id,
        employeeId: record.employeeId,
        employeeName: record.employee?.name || '未知员工',
        fromStation: record.fromStation?.name || '未知电站',
        toStation: record.toStation?.name || '未知电站',
        dispatchType: record.dispatchType,  // 修正：使用正确的字段名
        status: record.status,
        source: record.source,
        requestDate: new Date(record.createdAt).toLocaleDateString(),
        startDate: new Date(record.startDate).toLocaleDateString(),
        endDate: record.endDate ? new Date(record.endDate).toLocaleDateString() : undefined,
        reason: record.reason,
        aiQuery: record.aiQuery,
        riskAnalysis: record.riskAnalysis?.description,
        approvedBy: record.reviewer?.username,
        approvedAt: record.reviewDate ? new Date(record.reviewDate).toLocaleDateString() : undefined,
        rejectionReason: record.reviewComments
      }));

      return {
        records: transformedRecords,
        pagination: response.pagination || { current: 1, pageSize: 20, total: 0, pages: 0 }
      };
    }

    return { records: [], pagination: { current: 1, pageSize: 20, total: 0, pages: 0 } };
  }

  // 创建调度记录
  async createDispatch(request: CreateDispatchRequest): Promise<DispatchRecord> {
    return await apiClient.request('/dispatch/records', {
      method: 'POST',
      body: JSON.stringify(request),
    });
  }

  // 创建调度记录（别名方法）
  async createDispatchRecord(request: CreateDispatchRequest): Promise<DispatchRecord> {
    return this.createDispatch(request);
  }

  // 批量创建调度记录
  async createBatchDispatch(request: CreateBatchDispatchRequest): Promise<DispatchRecord[]> {
    return await apiClient.request('/dispatch/records/batch', {
      method: 'POST',
      body: JSON.stringify(request),
    });
  }

  // AI查询员工
  async aiQuery(request: AIQueryRequest): Promise<AIQueryResponse> {
    return await apiClient.request('/dispatch/ai/query', {
      method: 'POST',
      body: JSON.stringify(request),
    });
  }

  // 获取AI调度推荐
  async aiRecommendation(request: AIRecommendationRequest): Promise<AIRecommendationResponse> {
    return await apiClient.request('/dispatch/ai/recommend', {
      method: 'POST',
      body: JSON.stringify(request),
    });
  }

  // 增强版AI调度推荐
  async enhancedAiRecommendation(request: EnhancedAIRecommendationRequest): Promise<EnhancedAIRecommendationResponse> {
    return await apiClient.request('/dispatch/ai/enhanced-recommend', {
      method: 'POST',
      body: JSON.stringify(request),
    });
  }

  // 获取推荐配置
  async getRecommendationConfig(): Promise<{ success: boolean; config: RecommendationConfig }> {
    return await apiClient.request('/dispatch/ai/config', {
      method: 'GET',
    });
  }

  // 更新推荐配置
  async updateRecommendationConfig(config: Partial<RecommendationConfig>): Promise<{ success: boolean; message: string; config: Partial<RecommendationConfig> }> {
    return await apiClient.request('/dispatch/ai/config', {
      method: 'PUT',
      body: JSON.stringify(config),
    });
  }

  // 审核调度申请
  async approveDispatch(id: string, request: ApprovalRequest): Promise<DispatchRecord> {
    return await apiClient.request(`/dispatch/records/${id}/approve`, {
      method: 'POST',
      body: JSON.stringify(request),
    });
  }

  // 获取调度统计
  async getDispatchStats(): Promise<DispatchStats> {
    return await apiClient.request('/dispatch/stats', { method: 'GET' });
  }

  // 获取单个调度记录
  async getDispatchRecord(id: string): Promise<DispatchRecord> {
    return await apiClient.request(`/dispatch/records/${id}`, { method: 'GET' });
  }

  // 更新调度记录
  async updateDispatch(id: string, request: Partial<CreateDispatchRequest>): Promise<DispatchRecord> {
    return await apiClient.request(`/dispatch/records/${id}`, {
      method: 'PUT',
      body: JSON.stringify(request),
    });
  }

  // 删除调度记录
  async deleteDispatch(id: string): Promise<void> {
    await apiClient.request(`/dispatch/records/${id}`, { method: 'DELETE' });
  }
}

export const dispatchService = new DispatchService();