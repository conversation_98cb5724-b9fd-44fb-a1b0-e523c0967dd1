import React from 'react';
import { Employee, PowerStation } from '../../types';
import { formatEmployeeDisplay, getEmployeeStatusBadges } from '../../utils/employeeDisplayUtils';
import { Icons } from '../../constants';

interface EmployeeCardProps {
  employee: Employee;
  station: PowerStation;
  canEdit: boolean;
  onEdit: (employee: Employee) => void;
  onDelete: (employee: Employee) => void;
  isDispatchedIn?: boolean;
  isDispatchedOut?: boolean;
  dispatchInfo?: {
    fromStation?: string;
    toStation?: string;
  };
}

const EmployeeCard: React.FC<EmployeeCardProps> = ({
  employee,
  station,
  canEdit,
  onEdit,
  onDelete,
  isDispatchedIn = false,
  isDispatchedOut = false,
  dispatchInfo
}) => {
  const displayFormat = formatEmployeeDisplay(employee);
  const statusBadges = getEmployeeStatusBadges(employee);

  // 根据调度状态调整样式
  let cardClassName = `p-1.5 rounded-md border text-xs ${displayFormat.backgroundColor} ${displayFormat.borderColor}`;
  
  if (isDispatchedIn) {
    cardClassName = 'p-1.5 rounded-md border text-xs bg-green-600/30 border-green-400/70 shadow-md ring-1 ring-green-400/20';
  } else if (isDispatchedOut) {
    cardClassName = 'p-1.5 rounded-md border text-xs bg-red-600/25 border-red-400/60 shadow-sm';
  }

  return (
    <div className={cardClassName}>
      <div className="flex justify-between items-center gap-2">
        <span className={`font-bold whitespace-nowrap ${displayFormat.textColor}`}>
          {displayFormat.displayName}
          {displayFormat.statusText && (
            <span className="ml-1">{displayFormat.statusText}</span>
          )}
          {isDispatchedIn && dispatchInfo?.fromStation && (
            <span className="text-green-300 ml-1">
              (来自{dispatchInfo.fromStation})
            </span>
          )}
          {isDispatchedOut && dispatchInfo?.toStation && (
            <span className="text-red-300 ml-1">
              (调至{dispatchInfo.toStation})
            </span>
          )}
        </span>
        
        {canEdit && displayFormat.displayName !== '——' && displayFormat.displayName !== '(空缺)' && (
          <div className="flex space-x-1.5 flex-shrink-0">
            <button 
              onClick={() => onEdit(employee)} 
              className="text-cyan-400 hover:text-cyan-300"
              title="编辑员工"
            >
              <Icons.Edit className="w-3.5 h-3.5"/>
            </button>
            <button 
              onClick={() => onDelete(employee)} 
              className="text-red-500 hover:text-red-400"
              title="删除员工"
            >
              <Icons.Trash className="w-3.5 h-3.5"/>
            </button>
          </div>
        )}
      </div>
      
      {statusBadges.length > 0 && (
        <div className="flex flex-wrap gap-1 mt-1.5">
          {statusBadges.map((badge, index) => (
            <span 
              key={index}
              className={`text-xs px-1.5 py-0.5 rounded-full whitespace-nowrap font-semibold ${badge.className}`}
            >
              {badge.text}
            </span>
          ))}
        </div>
      )}
    </div>
  );
};

export default EmployeeCard;