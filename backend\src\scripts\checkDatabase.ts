import mongoose from 'mongoose';
import { PowerStation, Employee, Position, StationPosition, ConcurrentPosition } from '../models';

// 连接数据库
const connectDB = async () => {
  try {
    await mongoose.connect('mongodb://localhost:27017/power_station_management');
    console.log('✅ MongoDB 连接成功');
  } catch (error) {
    console.error('❌ MongoDB 连接失败:', error);
    process.exit(1);
  }
};

// 检查数据库数据
const checkDatabase = async () => {
  console.log('🔍 检查数据库数据...');
  
  await connectDB();
  
  try {
    // 统计各个集合的数据量
    const stats = {
      powerStations: await PowerStation.countDocuments(),
      positions: await Position.countDocuments(),
      employees: await Employee.countDocuments(),
      stationPositions: await StationPosition.countDocuments(),
      concurrentPositions: await ConcurrentPosition.countDocuments()
    };
    
    console.log('\n📊 数据库统计信息:');
    console.log(`  电站数量: ${stats.powerStations}`);
    console.log(`  岗位模板: ${stats.positions}`);
    console.log(`  员工数量: ${stats.employees}`);
    console.log(`  岗位配置: ${stats.stationPositions}`);
    console.log(`  兼职岗位: ${stats.concurrentPositions}`);
    
    // 显示一些示例数据
    if (stats.powerStations > 0) {
      console.log('\n🏭 电站示例:');
      const stations = await PowerStation.find().limit(3);
      stations.forEach(station => {
        console.log(`  - ${station.name} (${station.location.province})`);
      });
    }
    
    if (stats.positions > 0) {
      console.log('\n📋 岗位模板示例:');
      const positions = await Position.find().limit(5);
      positions.forEach(position => {
        console.log(`  - ${position.positionName} (${position.department})`);
      });
    }
    
    if (stats.employees > 0) {
      console.log('\n👥 员工示例:');
      const employees = await Employee.find().limit(5).populate('homeStationId', 'name');
      employees.forEach(employee => {
        console.log(`  - ${employee.name} (${employee.employeeType}) - 归属: ${(employee.homeStationId as any)?.name || '未知'}`);
      });
    }
    
    if (stats.stationPositions > 0) {
      console.log('\n🔧 岗位配置示例:');
      const stationPositions = await StationPosition.find()
        .limit(5)
        .populate('stationId', 'name')
        .populate('positionId', 'positionName')
        .populate('employeeId', 'name');
      
      stationPositions.forEach(sp => {
        const stationName = (sp.stationId as any)?.name || '未知电站';
        const positionName = (sp.positionId as any)?.positionName || '未知岗位';
        const employeeName = (sp.employeeId as any)?.name || '空缺';
        console.log(`  - ${stationName} - ${positionName}: ${employeeName} (${sp.status})`);
      });
    }
    
    if (stats.concurrentPositions > 0) {
      console.log('\n🔄 兼职岗位示例:');
      const concurrentPositions = await ConcurrentPosition.find()
        .limit(5)
        .populate('employeeId', 'name')
        .populate('stationId', 'name')
        .populate('positionId', 'positionName');
      
      concurrentPositions.forEach(cp => {
        const employeeName = (cp.employeeId as any)?.name || '未知员工';
        const stationName = (cp.stationId as any)?.name || '未知电站';
        const positionName = (cp.positionId as any)?.positionName || '未知岗位';
        console.log(`  - ${employeeName} 在 ${stationName} 兼任 ${positionName} (${cp.status})`);
      });
    }
    
  } catch (error) {
    console.error('❌ 检查数据库时出错:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n✅ 数据库连接已关闭');
  }
};

// 执行检查
if (require.main === module) {
  checkDatabase().catch(console.error);
}

export { checkDatabase };