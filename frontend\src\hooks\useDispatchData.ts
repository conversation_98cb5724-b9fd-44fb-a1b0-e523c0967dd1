import { useState, useEffect } from 'react';
import { dispatchService, DispatchRecord } from '../services/dispatchService';
import { getEmployees, apiClient } from '../services/api';
import { Employee } from '../types';

export const useDispatchData = () => {
  const [dispatchRecords, setDispatchRecords] = useState<DispatchRecord[]>([]);
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [stations, setStations] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [dataLoaded, setDataLoaded] = useState(false);

  const fetchStations = async () => {
    try {
      const data = await apiClient.request('/stations', { method: 'GET' });
      console.log('✅ 电站数据加载成功:', data?.length || 0);
      setStations(Array.isArray(data) ? data : []);
    } catch (error) {
      console.error('获取电站数据失败:', error);
      setStations([]);
    }
  };

  const fetchDispatchRecords = async () => {
    try {
      const response = await dispatchService.getDispatchRecords();
      if (response && response.records) {
        setDispatchRecords(response.records);
      } else if (Array.isArray(response)) {
        setDispatchRecords(response);
      } else {
        setDispatchRecords([]);
      }
    } catch (error) {
      console.error('获取调度记录失败:', error);
      setDispatchRecords([]);
    }
  };

  const fetchEmployees = async () => {
    try {
      const data = await getEmployees();
      console.log('✅ 调度中心员工数据加载成功:', data?.length || 0);
      setEmployees(Array.isArray(data) ? data : []);
    } catch (error) {
      console.error('获取员工数据失败:', error);
      setEmployees([]);
    }
  };

  const loadData = async () => {
    setLoading(true);
    try {
      await Promise.all([
        fetchDispatchRecords(),
        fetchEmployees(),
        fetchStations()
      ]);
      setDataLoaded(true);
      console.log('🎉 调度中心所有数据加载完成');
    } catch (error) {
      console.error('加载数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadData();
  }, []);

  return {
    dispatchRecords,
    employees,
    stations,
    loading,
    dataLoaded,
    fetchDispatchRecords,
    fetchEmployees,
    fetchStations,
    loadData
  };
};