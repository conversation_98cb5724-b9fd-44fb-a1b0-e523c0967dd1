import { Request, Response } from 'express';
import { ApiResponse } from '../types/index';
import { logger } from '../utils/logger';
import { asyncHand<PERSON> } from '../middleware';
import { Position } from '../models/Position';
import { StationPosition } from '../models/StationPosition';

import { Employee } from '../models/Employee';

// 获取所有岗位模板
export const getAllPositions = async (req: Request, res: Response): Promise<void> => {
  try {
    const positions = await Position.find({ isActive: true });
    
    const response: ApiResponse<typeof positions> = {
      success: true,
      data: positions
    };
    
    res.status(200).json(response);
  } catch (error) {
    logger.error('获取岗位模板失败:', error);
    const response: ApiResponse = {
      success: false,
      message: '获取岗位模板失败'
    };
    res.status(500).json(response);
  }
};

// 根据部门获取岗位模板
export const getPositionsByDepartment = async (req: Request, res: Response): Promise<void> => {
  try {
    const { department } = req.params;
    const positions = await Position.find({ 
      department, 
      isActive: true 
    });
    
    const response: ApiResponse<typeof positions> = {
      success: true,
      data: positions
    };
    
    res.status(200).json(response);
  } catch (error) {
    logger.error('根据部门获取岗位模板失败:', error);
    const response: ApiResponse = {
      success: false,
      message: '根据部门获取岗位模板失败'
    };
    res.status(500).json(response);
  }
};

// 获取电站岗位配置
export const getStationPositions = async (req: Request, res: Response): Promise<void> => {
  try {
    const { stationId } = req.params;
    const stationPositions = await StationPosition.find({ stationId })
      .populate('positionId', 'positionName department level')
      .populate('employeeId', 'name employeeType status')
      .populate('stationId', 'name');
    
    const response: ApiResponse<typeof stationPositions> = {
      success: true,
      data: stationPositions
    };
    
    res.status(200).json(response);
  } catch (error) {
    logger.error('获取电站岗位配置失败:', error);
    const response: ApiResponse = {
      success: false,
      message: '获取电站岗位配置失败'
    };
    res.status(500).json(response);
  }
};

// 获取空缺岗位
export const getVacantPositions = async (req: Request, res: Response): Promise<void> => {
  try {
    const { stationId } = req.params;
    const vacantPositions = await StationPosition.find({ 
      stationId,
      status: 'vacant'
    })
      .populate('positionId', 'positionName department level')
      .populate('stationId', 'name');
    
    const response: ApiResponse<typeof vacantPositions> = {
      success: true,
      data: vacantPositions
    };
    
    res.status(200).json(response);
  } catch (error) {
    logger.error('获取空缺岗位失败:', error);
    const response: ApiResponse = {
      success: false,
      message: '获取空缺岗位失败'
    };
    res.status(500).json(response);
  }
};

// 分配员工到岗位
export const assignEmployeeToPosition = async (req: Request, res: Response): Promise<void> => {
  try {
    const { stationPositionId } = req.params;
    const { employeeId } = req.body;
    
    const stationPosition = await StationPosition.findByIdAndUpdate(
      stationPositionId,
      { 
        employeeId,
        status: 'occupied'
      },
      { new: true }
    )
      .populate('positionId', 'positionName department level')
      .populate('employeeId', 'name employeeType status')
      .populate('stationId', 'name');
    
    if (!stationPosition) {
      const response: ApiResponse = {
        success: false,
        message: '岗位配置不存在'
      };
      res.status(404).json(response);
      return;
    }
    
    const response: ApiResponse<typeof stationPosition> = {
      success: true,
      data: stationPosition,
      message: '员工分配成功'
    };
    
    res.status(200).json(response);
  } catch (error) {
    logger.error('分配员工到岗位失败:', error);
    const response: ApiResponse = {
      success: false,
      message: '分配员工到岗位失败'
    };
    res.status(500).json(response);
  }
};

// 移除岗位员工
export const removeEmployeeFromPosition = async (req: Request, res: Response): Promise<void> => {
  try {
    const { stationPositionId } = req.params;
    
    const stationPosition = await StationPosition.findByIdAndUpdate(
      stationPositionId,
      { 
        $unset: { employeeId: 1 },
        status: 'vacant'
      },
      { new: true }
    )
      .populate('positionId', 'positionName department level')
      .populate('stationId', 'name');
    
    if (!stationPosition) {
      const response: ApiResponse = {
        success: false,
        message: '岗位配置不存在'
      };
      res.status(404).json(response);
      return;
    }
    
    const response: ApiResponse<typeof stationPosition> = {
      success: true,
      data: stationPosition,
      message: '员工移除成功'
    };
    
    res.status(200).json(response);
  } catch (error) {
    logger.error('移除岗位员工失败:', error);
    const response: ApiResponse = {
      success: false,
      message: '移除岗位员工失败'
    };
    res.status(500).json(response);
  }
};

// 获取电站岗位统计
export const getStationPositionStats = async (req: Request, res: Response): Promise<void> => {
  try {
    const { stationId } = req.params;

    // 获取该电站的所有岗位配置，按岗位分组
    const stationPositions = await StationPosition.find({ stationId })
      .populate('positionId', 'positionName')
      .populate('employeeId', 'employeeType homeStationId')
      .lean();

    // 按岗位分组，统计每个岗位是否已配置
    const positionGroups = stationPositions.reduce((acc: any, position: any) => {
      const positionName = position.positionId.positionName;

      if (!acc[positionName]) {
        acc[positionName] = {
          hasEmployee: false,
          totalInstances: 0,
          occupiedInstances: 0
        };
      }

      acc[positionName].totalInstances++;

      // 检查是否有归属该电站的正式员工（排除外委和调度）
      if (position.employeeId &&
          position.employeeId.homeStationId &&
          position.employeeId.homeStationId.toString() === stationId &&
          position.employeeId.employeeType !== 'external') {
        acc[positionName].hasEmployee = true;
        acc[positionName].occupiedInstances++;
      }

      return acc;
    }, {});

    // 统计已配置的岗位数（只要岗位有人就算配置）
    const totalUniquePositions = Object.keys(positionGroups).length;
    const configuredPositions = Object.values(positionGroups).filter((group: any) => group.hasEmployee).length;
    const unconfiguredPositions = totalUniquePositions - configuredPositions;

    // 计算总人员数（归属该电站的正式员工）
    const { Employee } = await import('../models');
    const totalEmployees = await Employee.countDocuments({
      homeStationId: stationId,
      status: 'active',
      employeeType: { $ne: 'external' }  // 排除外委
    });

    const stats = {
      total: totalUniquePositions,              // 标准岗位总数
      occupied: configuredPositions,            // 已配置岗位数
      vacant: unconfiguredPositions,            // 未配置岗位数
      totalEmployees: totalEmployees,           // 总员工数
      occupancyRate: totalUniquePositions > 0 ? (configuredPositions / totalUniquePositions * 100).toFixed(2) : '0'
    };
    
    const response: ApiResponse<typeof stats> = {
      success: true,
      data: stats
    };

    res.status(200).json(response);
  } catch (error) {
    logger.error('获取电站岗位统计失败:', error);
    const response: ApiResponse = {
      success: false,
      message: '获取电站岗位统计失败'
    };
    res.status(500).json(response);
  }
};

// 获取电站详细岗位配置统计
export const getStationDetailedPositionStats = async (req: Request, res: Response): Promise<void> => {
  try {
    const { stationId } = req.params;

    // 获取该电站的所有岗位配置，按岗位分组
    const stationPositions = await StationPosition.find({ stationId })
      .populate('positionId', 'positionName department level')
      .populate('employeeId', 'name employeeType status')
      .lean();

    // 按岗位分组统计
    const positionStats = stationPositions.reduce((acc: any, position: any) => {
      const positionName = position.positionId.positionName;

      if (!acc[positionName]) {
        acc[positionName] = {
          positionName,
          department: position.positionId.department,
          level: position.positionId.level,
          totalInstances: 0,
          occupiedInstances: 0,
          vacantInstances: 0,
          notConfiguredInstances: 0,
          instances: []
        };
      }

      acc[positionName].totalInstances++;
      acc[positionName].instances.push({
        instance: position.instance,
        status: position.status,
        employee: position.employeeId ? {
          name: position.employeeId.name,
          employeeType: position.employeeId.employeeType
        } : null
      });

      // 统计各状态数量
      if (position.status === 'occupied') {
        acc[positionName].occupiedInstances++;
      } else if (position.status === 'vacant') {
        acc[positionName].vacantInstances++;
      } else if (position.status === 'not_configured') {
        acc[positionName].notConfiguredInstances++;
      }

      return acc;
    }, {});

    // 转换为数组并排序
    const detailedStats = Object.values(positionStats).sort((a: any, b: any) =>
      a.positionName.localeCompare(b.positionName)
    );

    const response: ApiResponse<typeof detailedStats> = {
      success: true,
      data: detailedStats
    };

    res.status(200).json(response);
  } catch (error) {
    logger.error('获取电站详细岗位统计失败:', error);
    const response: ApiResponse = {
      success: false,
      message: '获取电站详细岗位统计失败'
    };
    res.status(500).json(response);
  }
};