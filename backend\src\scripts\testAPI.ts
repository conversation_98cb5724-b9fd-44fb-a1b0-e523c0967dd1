import axios from 'axios';

const API_BASE_URL = 'http://localhost:3002/api/v1';

const testAPIs = async () => {
  console.log('🧪 开始测试API端点...\n');
  
  let authToken = '';
  
  try {
    // 首先尝试登录获取token
    console.log('🔐 尝试登录获取认证token...');
    try {
      const loginResponse = await axios.post(`${API_BASE_URL}/auth/login`, {
        username: 'admin',
        password: 'admin123'
      });
      
      if (loginResponse.status === 200) {
        authToken = loginResponse.data.data.token;
        console.log('✅ 登录成功，获取到token\n');
      }
    } catch (loginError: any) {
      console.log('⚠️ 登录失败，可能需要先创建管理员用户');
      console.log('尝试注册管理员用户...');
      
      try {
        await axios.post(`${API_BASE_URL}/auth/register`, {
          username: 'admin',
          password: 'admin123',
          email: '<EMAIL>',
          role: 'admin'
        });
        
        // 注册后再次登录
        const loginResponse = await axios.post(`${API_BASE_URL}/auth/login`, {
          username: 'admin',
          password: 'admin123'
        });
        
        authToken = loginResponse.data.data.token;
        console.log('✅ 注册并登录成功\n');
      } catch (registerError: any) {
        console.log('⚠️ 注册失败，继续测试公开API...\n');
      }
    }

    // 设置认证头
    const authHeaders = authToken ? { Authorization: `Bearer ${authToken}` } : {};

    // 测试电站列表
    console.log('📡 测试电站列表API...');
    const stationsResponse = await axios.get(`${API_BASE_URL}/stations`, { headers: authHeaders });
    
    if (stationsResponse.status === 200) {
      const stations = stationsResponse.data.data || stationsResponse.data;
      console.log(`✅ 电站数量: ${stations.length}`);
      console.log(`电站列表: ${stations.map((s: any) => s.name).join(', ')}\n`);
    }

    // 测试员工列表（需要认证）
    if (authToken) {
      console.log('📡 测试员工列表API...');
      const employeesResponse = await axios.get(`${API_BASE_URL}/employees`, { headers: authHeaders });
      
      if (employeesResponse.status === 200) {
        const employees = employeesResponse.data.data || employeesResponse.data;
        console.log(`✅ 员工数量: ${employees.length}`);
        
        // 统计员工状态
        const statusCounts = employees.reduce((acc: any, emp: any) => {
          acc[emp.status] = (acc[emp.status] || 0) + 1;
          return acc;
        }, {});
        console.log('员工状态统计:', statusCounts);
        
        // 提取职位信息
        const positions = [...new Set(employees.map((emp: any) => emp.position))];
        console.log(`✅ 职位数量: ${positions.length}`);
        console.log(`职位列表: ${positions.slice(0, 10).join(', ')}${positions.length > 10 ? '...' : ''}\n`);
        
        // 提取部门信息
        const departments = [...new Set(employees.map((emp: any) => emp.department))];
        console.log(`✅ 部门数量: ${departments.length}`);
        console.log(`部门列表: ${departments.join(', ')}\n`);
        
        // 检查空缺记录
        const vacantEmployees = employees.filter((emp: any) => emp.status === 'vacant');
        console.log(`⚠️ 空缺记录数量: ${vacantEmployees.length}`);
        if (vacantEmployees.length > 0) {
          console.log('空缺记录示例:');
          vacantEmployees.slice(0, 5).forEach((emp: any) => {
            console.log(`  - ${emp.name} (${emp.position}) - ${emp.department}`);
          });
          console.log('');
        }
      }

      // 测试搜索功能
      console.log('📡 测试搜索功能...');
      const searchResponse = await axios.get(`${API_BASE_URL}/employees?station=国能共和项目`, { headers: authHeaders });
      
      if (searchResponse.status === 200) {
        const searchResults = searchResponse.data.data || searchResponse.data;
        console.log(`✅ 搜索结果数量: ${searchResults.length}`);
      }
    } else {
      console.log('⚠️ 无认证token，跳过员工API测试');
    }

  } catch (error: any) {
    console.error('❌ API测试失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', error.response.data);
    }
  }
};

testAPIs();