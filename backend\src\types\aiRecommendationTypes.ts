// AI推荐规则配置类型定义

export interface RecommendationRule {
  id: string;
  name: string;
  description: string;
  weight: number; // 权重 0-100
  enabled: boolean;
  parameters: Record<string, any>;
}

export interface RecommendationConfig {
  // 基础规则
  skillMatchWeight: number; // 技能匹配权重
  experienceWeight: number; // 经验权重
  positionMatchWeight: number; // 职位匹配权重
  locationWeight: number; // 地理位置权重
  
  // 疲劳度和连续调度规则
  maxContinuousDispatches: number; // 最大连续调度次数
  minRestDaysBetweenDispatches: number; // 调度间最少休息天数
  fatigueThreshold: number; // 疲劳度阈值
  
  // 风险控制规则
  minStaffRemaining: number; // 调出后最少剩余人员
  criticalPositionProtection: string[]; // 关键岗位保护列表
  emergencyResponseTime: number; // 应急响应时间要求（小时）
  
  // 优先级规则
  preferInternalTransfer: boolean; // 优先内部调动
  preferExperiencedStaff: boolean; // 优先经验丰富员工
  considerTrainingNeeds: boolean; // 考虑培训需求
  
  // 特殊情况处理
  allowOvertimeDispatch: boolean; // 允许加班调度
  allowCrossRegionDispatch: boolean; // 允许跨区域调度
  requireManagerApproval: boolean; // 需要经理审批
  
  // AI增强配置
  dashscope?: {
    apiKey: string;
    baseUrl?: string;
    model?: string;
    temperature?: number;
    maxTokens?: number;
  };
}

export interface EmployeeBusinessStatus {
  employeeId: string;
  // 调度历史统计
  totalDispatches: number; // 总调度次数
  recentDispatches: number; // 近期调度次数（30天内）
  continuousDispatches: number; // 连续调度次数
  lastDispatchEndDate?: Date; // 最后一次调度结束时间
  
  // 疲劳度评估
  fatigueScore: number; // 疲劳度评分 0-100
  workloadScore: number; // 工作负荷评分 0-100
  
  // 能力评估
  skillProficiency: Record<string, number>; // 技能熟练度
  performanceRating: number; // 绩效评分
  adaptabilityScore: number; // 适应性评分
  
  // 可用性状态
  isAvailable: boolean; // 是否可调度
  unavailableReason?: string; // 不可调度原因
  availableFrom?: Date; // 可调度开始时间
  availableUntil?: Date; // 可调度结束时间
  
  // 培训和发展
  trainingNeeds: string[]; // 培训需求
  careerGoals: string[]; // 职业目标
  mentorshipRole?: 'mentor' | 'mentee' | 'none'; // 导师角色
}

export interface EnhancedRecommendation {
  employeeId: string;
  employee: any; // 员工基本信息
  businessStatus: EmployeeBusinessStatus; // 业务状态
  
  // 推荐评分
  overallScore: number; // 综合评分 0-100
  skillMatchScore: number; // 技能匹配评分
  experienceScore: number; // 经验评分
  availabilityScore: number; // 可用性评分
  riskScore: number; // 风险评分
  
  // 推荐详情
  matchedSkills: string[]; // 匹配的技能
  missingSkills: string[]; // 缺失的技能
  strengthReasons: string[]; // 推荐优势
  riskFactors: string[]; // 风险因素
  
  // 调度建议
  recommendedDuration: number; // 建议调度天数
  suggestedStartDate: Date; // 建议开始时间
  alternativeOptions: string[]; // 替代方案
  
  // 发展机会
  learningOpportunities: string[]; // 学习机会
  skillDevelopment: string[]; // 技能发展
}

export interface RecommendationRequest {
  requirement: string; // 自然语言需求描述
  targetStationId?: string; // 目标电站ID
  requiredSkills?: string[]; // 必需技能
  preferredExperience?: number; // 期望经验年限
  urgencyLevel: 'low' | 'medium' | 'high' | 'emergency'; // 紧急程度
  duration?: number; // 预期调度天数
  startDate?: Date; // 期望开始时间
  maxCandidates?: number; // 最大候选人数
  config?: Partial<RecommendationConfig>; // 自定义配置
}

export interface RecommendationResponse {
  recommendations: EnhancedRecommendation[];
  summary: {
    totalCandidates: number;
    averageScore: number;
    riskLevel: 'low' | 'medium' | 'high' | 'critical';
    confidence: number;
  };
  analysis: string;
  riskAssessment: string;
  alternatives: string[];
  configUsed: RecommendationConfig;
}