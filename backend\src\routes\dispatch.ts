import express from 'express';
import {
  getDispatchRecords,
  createDispatchRecord,
  createBatchDispatchRecords,
  aiQueryEmployees,
  getAIDispatchRecommendations,
  handleAiQuery,
  handleAiRecommendation,
  handleEnhancedAiRecommendation,
  getRecommendationConfig,
  updateRecommendationConfig,
  reviewDispatchRecord,
  getDispatchStatistics
} from '../controllers/dispatchController';

const router = express.Router();

// 调度记录管理
router.get('/records', getDispatchRecords);
router.post('/records', createDispatchRecord);
router.post('/records/batch', createBatchDispatchRecords);
router.put('/records/:id/review', reviewDispatchRecord);

// AI功能
router.post('/ai/query', handleAiQuery);
router.post('/ai/recommend', handleAiRecommendation);
router.post('/ai/enhanced-recommend', handleEnhancedAiRecommendation);

// 推荐规则配置
router.get('/ai/config', getRecommendationConfig);
router.put('/ai/config', updateRecommendationConfig);

// 兼容旧接口
router.post('/ai/query-legacy', aiQueryEmployees);
router.post('/ai/recommend-legacy', getAIDispatchRecommendations);

// 统计数据
router.get('/statistics', getDispatchStatistics);

export default router;