import React, { useState, useEffect } from 'react';
import { Modal, Form, Input, InputNumber, Switch, Slider, Select, Button, Card, Row, Col, Divider, message, Tooltip } from 'antd';
import { InfoCircleOutlined, SettingOutlined, SaveOutlined, ReloadOutlined } from '@ant-design/icons';
import { RecommendationConfig } from '../services/dispatchService';

interface RecommendationConfigModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (config: Partial<RecommendationConfig>) => void;
  initialConfig?: RecommendationConfig;
  loading?: boolean;
}

const RecommendationConfigModal: React.FC<RecommendationConfigModalProps> = ({
  isOpen,
  onClose,
  onSave,
  initialConfig,
  loading = false
}) => {
  const [form] = Form.useForm();
  const [config, setConfig] = useState<Partial<RecommendationConfig>>({});

  useEffect(() => {
    if (initialConfig) {
      setConfig(initialConfig);
      form.setFieldsValue(initialConfig);
    }
  }, [initialConfig, form]);

  const handleSave = async () => {
    try {
      const values = await form.validateFields();
      onSave(values);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  const handleReset = () => {
    if (initialConfig) {
      form.setFieldsValue(initialConfig);
      setConfig(initialConfig);
    }
  };

  const weightMarks = {
    0: '0%',
    25: '25%',
    50: '50%',
    75: '75%',
    100: '100%'
  };

  return (
    <Modal
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          <SettingOutlined />
          <span>AI推荐规则配置</span>
        </div>
      }
      open={isOpen}
      onCancel={onClose}
      width={800}
      footer={[
        <Button key="reset" icon={<ReloadOutlined />} onClick={handleReset}>
          重置
        </Button>,
        <Button key="cancel" onClick={onClose}>
          取消
        </Button>,
        <Button 
          key="save" 
          type="primary" 
          icon={<SaveOutlined />}
          loading={loading}
          onClick={handleSave}
        >
          保存配置
        </Button>
      ]}
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={initialConfig}
        onValuesChange={(changedValues, allValues) => setConfig(allValues)}
      >
        {/* 权重配置 */}
        <Card title="评分权重配置" size="small" style={{ marginBottom: 16 }}>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label={
                  <span>
                    技能匹配权重
                    <Tooltip title="员工技能与岗位要求匹配度的权重">
                      <InfoCircleOutlined style={{ marginLeft: 4, color: '#1890ff' }} />
                    </Tooltip>
                  </span>
                }
                name="skillMatchWeight"
                rules={[{ required: true, message: '请设置技能匹配权重' }]}
              >
                <Slider
                  marks={weightMarks}
                  min={0}
                  max={100}
                  tooltip={{ formatter: (value) => `${value}%` }}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label={
                  <span>
                    经验权重
                    <Tooltip title="员工工作经验和年限的权重">
                      <InfoCircleOutlined style={{ marginLeft: 4, color: '#1890ff' }} />
                    </Tooltip>
                  </span>
                }
                name="experienceWeight"
                rules={[{ required: true, message: '请设置经验权重' }]}
              >
                <Slider
                  marks={weightMarks}
                  min={0}
                  max={100}
                  tooltip={{ formatter: (value) => `${value}%` }}
                />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label={
                  <span>
                    职位匹配权重
                    <Tooltip title="员工职位与目标岗位匹配度的权重">
                      <InfoCircleOutlined style={{ marginLeft: 4, color: '#1890ff' }} />
                    </Tooltip>
                  </span>
                }
                name="positionMatchWeight"
                rules={[{ required: true, message: '请设置职位匹配权重' }]}
              >
                <Slider
                  marks={weightMarks}
                  min={0}
                  max={100}
                  tooltip={{ formatter: (value) => `${value}%` }}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label={
                  <span>
                    地理位置权重
                    <Tooltip title="员工当前位置与目标电站距离的权重">
                      <InfoCircleOutlined style={{ marginLeft: 4, color: '#1890ff' }} />
                    </Tooltip>
                  </span>
                }
                name="locationWeight"
                rules={[{ required: true, message: '请设置地理位置权重' }]}
              >
                <Slider
                  marks={weightMarks}
                  min={0}
                  max={100}
                  tooltip={{ formatter: (value) => `${value}%` }}
                />
              </Form.Item>
            </Col>
          </Row>
        </Card>

        {/* 疲劳度和调度规则 */}
        <Card title="疲劳度和调度控制" size="small" style={{ marginBottom: 16 }}>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                label={
                  <span>
                    最大连续调度次数
                    <Tooltip title="员工连续被调度的最大次数">
                      <InfoCircleOutlined style={{ marginLeft: 4, color: '#1890ff' }} />
                    </Tooltip>
                  </span>
                }
                name="maxContinuousDispatches"
                rules={[{ required: true, message: '请设置最大连续调度次数' }]}
              >
                <InputNumber min={1} max={10} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label={
                  <span>
                    最小休息天数
                    <Tooltip title="两次调度之间的最小休息天数">
                      <InfoCircleOutlined style={{ marginLeft: 4, color: '#1890ff' }} />
                    </Tooltip>
                  </span>
                }
                name="minRestDaysBetweenDispatches"
                rules={[{ required: true, message: '请设置最小休息天数' }]}
              >
                <InputNumber min={0} max={30} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label={
                  <span>
                    疲劳度阈值
                    <Tooltip title="超过此阈值的员工将被标记为高疲劳">
                      <InfoCircleOutlined style={{ marginLeft: 4, color: '#1890ff' }} />
                    </Tooltip>
                  </span>
                }
                name="fatigueThreshold"
                rules={[{ required: true, message: '请设置疲劳度阈值' }]}
              >
                <InputNumber min={0} max={100} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>
        </Card>

        {/* 风险控制 */}
        <Card title="风险控制规则" size="small" style={{ marginBottom: 16 }}>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label={
                  <span>
                    最少保留人员数
                    <Tooltip title="每个电站必须保留的最少人员数量">
                      <InfoCircleOutlined style={{ marginLeft: 4, color: '#1890ff' }} />
                    </Tooltip>
                  </span>
                }
                name="minStaffRemaining"
                rules={[{ required: true, message: '请设置最少保留人员数' }]}
              >
                <InputNumber min={1} max={20} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label={
                  <span>
                    应急响应时间(小时)
                    <Tooltip title="紧急情况下的最大响应时间">
                      <InfoCircleOutlined style={{ marginLeft: 4, color: '#1890ff' }} />
                    </Tooltip>
                  </span>
                }
                name="emergencyResponseTime"
                rules={[{ required: true, message: '请设置应急响应时间' }]}
              >
                <InputNumber min={1} max={24} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>
          <Form.Item
            label={
              <span>
                关键岗位保护
                <Tooltip title="这些岗位的员工调度需要特别审慎">
                  <InfoCircleOutlined style={{ marginLeft: 4, color: '#1890ff' }} />
                </Tooltip>
              </span>
            }
            name="criticalPositionProtection"
          >
            <Select
              mode="tags"
              style={{ width: '100%' }}
              placeholder="输入关键岗位名称"
              options={[
                { label: '站长', value: '站长' },
                { label: '值长', value: '值长' },
                { label: '安全员', value: '安全员' },
                { label: '技术员', value: '技术员' },
                { label: '运维主管', value: '运维主管' }
              ]}
            />
          </Form.Item>
        </Card>

        {/* 优先级规则 */}
        <Card title="优先级和策略规则" size="small">
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="优先内部调动"
                name="preferInternalTransfer"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
              <Form.Item
                label="优先经验丰富员工"
                name="preferExperiencedStaff"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
              <Form.Item
                label="考虑培训需求"
                name="considerTrainingNeeds"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="允许加班调度"
                name="allowOvertimeDispatch"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
              <Form.Item
                label="允许跨区域调度"
                name="allowCrossRegionDispatch"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
              <Form.Item
                label="需要管理层审批"
                name="requireManagerApproval"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
          </Row>
        </Card>
      </Form>
    </Modal>
  );
};

export default RecommendationConfigModal;