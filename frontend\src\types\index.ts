
export enum UserRole {
  ADMIN = 'admin',
  HR = 'hr',
  BUSINESS = 'business'
}

export interface User {
  id: string;
  email: string;
  name: string;
  role: UserRole;
  createdAt: Date;
  updatedAt: Date;
}

export enum EmploymentStatus {
  ACTIVE = 'active',
  LEARNING = 'learning', 
  INACTIVE = 'inactive',
  VACANT = 'vacant',
  PENDING = 'pending',
  EXTERNAL_CONTRACT = 'external_contract',
  RESIGNED = 'resigned',
}

export enum EmployeeType {
  FULL_TIME = 'full_time',
  PART_TIME = 'part_time',
  INTERN = 'intern',
  EXTERNAL = 'external',
  BORROWED = 'borrowed',
}

export interface Employee {
  id: string;
  name: string;
  position: string;
  department: string;
  employeeType: EmployeeType | 'full_time' | 'part_time' | 'intern' | 'external' | 'borrowed';
  experience?: number; // in years
  currentStationId: string;
  homeStationId: string; // Employee's permanent base
  status: EmploymentStatus | 'active' | 'learning' | 'inactive' | 'vacant' | 'pending' | 'external_contract' | 'resigned';
  level?: string; //职级
  phone?: string;
  email?: string;
  hireDate: Date | string;
  resumeUrl?: string; // This can be a summary or a full resume text
  scheduleFrequency?: number;
  lastScheduled?: Date;
  totalScheduledDuration?: number; // in days
  skills?: string[]; // 技能列表
  certifications?: string[]; // 证书列表
  notes?: string;
  createdAt?: Date | string;
  updatedAt?: Date | string;
  // 调度状态相关字段（虚拟字段）
  dispatchStatus?: 'stationed' | 'dispatched'; // 在岗 | 调度中
  dispatchType?: 'outbound' | null; // 调出 | 无调度
  // 电站信息（populate后的字段）
  homeStation?: {
    _id: string;
    name: string;
  };
  currentStation?: {
    _id: string;
    name: string;
  };
  // 向后兼容
  stationId?: string;
}

export interface PowerStation {
  id: string;
  name: string;
  location: {
    province: string;
    city: string;
    address: string;
    coordinates: {
      latitude: number;
      longitude: number;
    };
  };
  type: 'solar' | 'wind' | 'hydro' | 'thermal' | 'nuclear' | 'hybrid';
  capacity: number;
  commissionDate: string | Date;
  status: 'active' | 'maintenance' | 'offline';
  createdAt?: string | Date;
  updatedAt?: string | Date;
  // 可选的前端计算字段
  coords?: { top: string; left: string }; // for map positioning
  staffing?: {
    [position: string]: { current: number; required: number };
  };
  annualGeneration?: number; // MWh
  totalGeneration?: number; // MWh
}

export enum WorkOrderStatus {
  OPEN = '待处理',
  IN_PROGRESS = '处理中',
  COMPLETED = '已完成',
  CLOSED = '已销单'
}

export interface WorkOrder {
  id: string;
  title: string;
  description: string;
  stationId: string;
  createdBy: string;
  assignedTo?: string;
  status: WorkOrderStatus;
  createdAt: Date;
  type: '报修' | '定期维护' | '采购';
}

export interface ParsedSchedulingRequest {
  targetStation: string;
  startDate: string;
  durationDays: number;
  requests: {
    position: string;
    count: number;
    level?: string;
  }[];
}