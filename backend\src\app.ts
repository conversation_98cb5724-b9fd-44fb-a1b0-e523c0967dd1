import express from 'express';
import { createServer } from 'http';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import morgan from 'morgan';
import { connectDatabase } from './config/database';
import { errorHandler, notFound, generalLimiter } from './middleware';
import routes from './routes';
import websocketRoutes, { initializeWebSocket } from './routes/websocket';
import config from './config';

const app = express();
const server = createServer(app);

// 安全中间件
app.use(helmet());

// CORS配置
app.use(cors({
  origin: config.cors.origin,
  credentials: true
}));

// 压缩响应
app.use(compression());

// 请求日志
if (config.nodeEnv === 'development') {
  app.use(morgan('dev'));
} else {
  app.use(morgan('combined'));
}

// 解析请求体
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 全局限流
app.use(generalLimiter);

// API路由
app.use('/api/v1', routes);
app.use('/api/v1/websocket', websocketRoutes);

// 根路径
app.get('/', (req, res) => {
  res.json({
    success: true,
    message: '电站运维管理系统 API',
    version: '1.0.0',
    documentation: '/api/v1/health'
  });
});

// 错误处理中间件
app.use(notFound);
app.use(errorHandler);

// 启动服务器
const startServer = async (): Promise<void> => {
  try {
    // 连接数据库
    await connectDatabase();
    
    // 初始化WebSocket服务
    const websocketService = initializeWebSocket(server);
    
    // 启动服务器
    const PORT = config.port;
    server.listen(PORT, () => {
      console.log(`🚀 服务器运行在端口 ${PORT}`);
      console.log(`🌍 环境: ${config.nodeEnv}`);
      console.log(`📡 API地址: http://localhost:${PORT}/api/v1`);
      console.log(`🏥 健康检查: http://localhost:${PORT}/api/v1/health`);
      console.log(`🔌 WebSocket服务已启动`);
      console.log(`📊 实时数据API: http://localhost:${PORT}/api/v1/websocket/realtime`);
    });
  } catch (error) {
    console.error('❌ 服务器启动失败:', error);
    process.exit(1);
  }
};

// 优雅关闭
process.on('SIGTERM', () => {
  console.log('🛑 收到SIGTERM信号，正在关闭服务器...');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('🛑 收到SIGINT信号，正在关闭服务器...');
  process.exit(0);
});

// 未捕获的异常处理
process.on('uncaughtException', (error) => {
  console.error('💥 未捕获的异常:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('💥 未处理的Promise拒绝:', reason);
  process.exit(1);
});

// 启动应用
if (require.main === module) {
  startServer();
}

export default app;

