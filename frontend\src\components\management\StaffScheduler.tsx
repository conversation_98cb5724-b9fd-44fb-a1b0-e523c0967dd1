import React, { useState, useEffect } from 'react';
import { Icons } from '../../constants';
import { getEmployees, apiClient } from '../../services/api';
import { Employee as ApiEmployee } from '../../types';
import { getAllEnums, EnumData, EnumOption } from '../../services/enumService';

// 扩展API员工类型以包含调度相关字段
interface Employee extends ApiEmployee {
  currentStationName?: string;
  homeStationName?: string;
  experience?: number;
  skills?: string[];
  availability?: boolean;
}

interface ScheduleRequest {
  id: string;
  fromStationId: string;
  fromStationName: string;
  toStationId: string;
  toStationName: string;
  position: string;
  urgency: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
  startDate: Date;
  endDate: Date;
  reason: string;
  status: 'PENDING' | 'APPROVED' | 'REJECTED';
  createdBy: string;
  createdAt: Date;
}

const StaffScheduler: React.FC = () => {
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [scheduleRequests, setScheduleRequests] = useState<ScheduleRequest[]>([]);
  const [selectedEmployee, setSelectedEmployee] = useState<Employee | null>(null);
  const [showScheduleModal, setShowScheduleModal] = useState(false);
  const [activeTab, setActiveTab] = useState<'employees' | 'requests'>('employees');
  const [enumData, setEnumData] = useState<EnumData | null>(null);
  const [stations, setStations] = useState<Array<{id: string, name: string}>>([]);

  // 加载真实数据
  useEffect(() => {
    const loadData = async () => {
      try {
        console.log('🔄 StaffScheduler: 开始加载数据...');

        // 并行加载所有数据
        const [employeesData, dispatchResponse, enumsData, stationsResponse] = await Promise.all([
          getEmployees(),
          apiClient.request('/dispatch/records?limit=100', { method: 'GET' }),
          getAllEnums(),
          apiClient.request('/stations', { method: 'GET' }).catch(() => ({ data: [] }))
        ]);

        console.log('✅ StaffScheduler: 员工数据加载完成:', employeesData?.length || 0);
        
        const dispatchRecords = dispatchResponse?.data?.records || [];
        console.log('✅ StaffScheduler: 调度记录加载完成:', dispatchRecords?.length || 0);

        // 设置枚举数据
        setEnumData(enumsData);
        console.log('✅ StaffScheduler: 枚举数据加载完成');

        // 设置电站数据
        const stationsData = stationsResponse?.data || [];
        setStations(stationsData);
        console.log('✅ StaffScheduler: 电站数据加载完成:', stationsData?.length || 0);

        // 处理员工数据，添加调度相关信息
        const processedEmployees: Employee[] = (employeesData || []).map(emp => {
          // 获取电站名称
          const getStationName = (stationObj: any) => {
            if (typeof stationObj === 'string') return stationObj;
            return stationObj?.name || '未知电站';
          };

          const currentStationName = getStationName(emp.currentStationId);
          const homeStationName = getStationName(emp.homeStationId);

          // 查找活跃调度记录
          const activeDispatch = dispatchRecords.find((record: any) =>
            record.employeeId === emp.id &&
            (record.status === 'active' || record.status === 'approved')
          );

          return {
            ...emp,
            currentStationName,
            homeStationName,
            experience: Math.floor(Math.random() * 15) + 1, // 临时随机经验值
            skills: emp.skills || [], // 使用API中的技能数据
            availability: emp.status === 'active' && !activeDispatch, // 在职且未被调度的员工可调度
            // 调度相关信息
            isDispatched: !!activeDispatch,
            dispatchRecord: activeDispatch
          };
        });

        setEmployees(processedEmployees);
        console.log('✅ StaffScheduler: 处理后的员工数据:', processedEmployees.length);

      } catch (error) {
        console.error('❌ StaffScheduler: 加载数据失败:', error);
        setEmployees([]);
      }
    };

    loadData();

    const mockRequests: ScheduleRequest[] = [
      {
        id: 'SR001',
        fromStationId: 'S001',
        fromStationName: '青海中控50MW',
        toStationId: 'S003',
        toStationName: '格尔木西勘院',
        position: '电气专工',
        urgency: 'HIGH',
        startDate: new Date('2024-01-20'),
        endDate: new Date('2024-01-27'),
        reason: '格尔木站电气专工请假，需要临时支援',
        status: 'PENDING',
        createdBy: '韩会珍',
        createdAt: new Date('2024-01-15T10:30:00')
      },
      {
        id: 'SR002',
        fromStationId: 'S002',
        fromStationName: '青海中控10MW',
        toStationId: 'S004',
        toStationName: '吐鲁番浙火',
        position: '汽机专工',
        urgency: 'MEDIUM',
        startDate: new Date('2024-01-25'),
        endDate: new Date('2024-02-05'),
        reason: '吐鲁番站汽机大修，需要技术支援',
        status: 'APPROVED',
        createdBy: '杨一啸',
        createdAt: new Date('2024-01-12T14:20:00')
      }
    ];

    // 暂时使用模拟的调度请求数据，后续可以从API获取
    setScheduleRequests(mockRequests);
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-500';
      case 'pending': return 'bg-yellow-500';
      case 'learning': return 'bg-blue-500';
      case 'vacant': return 'bg-red-500';
      case 'external_contract': return 'bg-gray-500';
      case 'LEAVE': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'LOW': return 'text-green-400';
      case 'MEDIUM': return 'text-yellow-400';
      case 'HIGH': return 'text-orange-400';
      case 'URGENT': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  const getStatusText = (status: string) => {
    // 优先使用枚举数据中的员工状态标签
    if (enumData?.employeeStatus) {
      const statusOption = enumData.employeeStatus.find(option => option.value === status);
      if (statusOption) return statusOption.label;
    }
    
    // 后备硬编码映射
    switch (status) {
      case 'active': return '在岗';
      case 'pending': return '待岗';
      case 'learning': return '学习中';
      case 'vacant': return '空缺';
      case 'external_contract': return '外委';
      case 'LEAVE': return '请假';
      default: return status;
    }
  };

  const getRequestStatusText = (status: string) => {
    // 优先使用枚举数据中的调度状态标签
    if (enumData?.dispatchStatus) {
      const statusOption = enumData.dispatchStatus.find(option => option.value === status.toLowerCase());
      if (statusOption) return statusOption.label;
    }
    
    // 后备硬编码映射
    switch (status) {
      case 'PENDING': return '待审批';
      case 'APPROVED': return '已批准';
      case 'REJECTED': return '已拒绝';
      default: return status;
    }
  };

  const getRequestStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING': return 'bg-yellow-500';
      case 'APPROVED': return 'bg-green-500';
      case 'REJECTED': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  const getUrgencyText = (urgency: string) => {
    // 优先使用枚举数据中的紧急程度标签
    if (enumData?.urgency) {
      const urgencyOption = enumData.urgency.find(option => option.value === urgency);
      if (urgencyOption) return urgencyOption.label;
    }
    
    // 后备硬编码映射
    switch (urgency) {
      case 'LOW': return '低';
      case 'MEDIUM': return '中';
      case 'HIGH': return '高';
      case 'URGENT': return '紧急';
      default: return urgency;
    }
  };

  return (
    <div className="bg-slate-900/50 backdrop-blur-sm border border-cyan-400/30 rounded-lg p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-bold text-white flex items-center">
          <Icons.Users className="w-6 h-6 text-cyan-300 mr-2" />
          人员调度管理
        </h2>
        <button
          onClick={() => {
            console.log('🔄 点击新建调度按钮 (StaffScheduler)');
            setShowScheduleModal(true);
            console.log('✅ 设置showScheduleModal为true');
          }}
          className="bg-cyan-500 hover:bg-cyan-600 text-white px-4 py-2 rounded-lg flex items-center transition-colors"
        >
          <Icons.Calendar className="w-4 h-4 mr-2" />
          新建调度
        </button>
      </div>

      {/* 标签页 */}
      <div className="flex space-x-1 mb-6">
        <button
          onClick={() => setActiveTab('employees')}
          className={`px-4 py-2 rounded-lg transition-colors ${
            activeTab === 'employees'
              ? 'bg-cyan-500 text-white'
              : 'bg-slate-700 text-gray-300 hover:bg-slate-600'
          }`}
        >
          人员列表
        </button>
        <button
          onClick={() => setActiveTab('requests')}
          className={`px-4 py-2 rounded-lg transition-colors ${
            activeTab === 'requests'
              ? 'bg-cyan-500 text-white'
              : 'bg-slate-700 text-gray-300 hover:bg-slate-600'
          }`}
        >
          调度申请
        </button>
      </div>

      {/* 人员列表 */}
      {activeTab === 'employees' && (
        <div className="space-y-4">
          {employees.map((employee) => (
            <div
              key={employee.id}
              className="bg-slate-800/50 border border-gray-600 rounded-lg p-4 hover:border-cyan-400/50 transition-colors"
            >
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-2">
                    <h3 className="text-lg font-semibold text-white">{employee.name}</h3>
                    <span className="text-cyan-300">{employee.position}</span>
                    <span className={`px-2 py-1 rounded-full text-xs text-white ${getStatusColor(employee.status)}`}>
                      {getStatusText(employee.status)}
                    </span>
                    {employee.availability ? (
                      <span className="text-green-400 text-sm">可调度</span>
                    ) : (
                      <span className="text-red-400 text-sm">不可调度</span>
                    )}
                  </div>
                  
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm text-gray-400 mb-3">
                    <div>
                      <span className="text-gray-500">当前电站：</span>
                      <span className="text-white">{employee.currentStationName}</span>
                    </div>
                    <div>
                      <span className="text-gray-500">归属电站：</span>
                      <span className="text-white">{employee.homeStationName}</span>
                    </div>
                    <div>
                      <span className="text-gray-500">工作经验：</span>
                      <span className="text-white">{employee.experience} 年</span>
                    </div>
                  </div>

                  <div className="flex flex-wrap gap-2">
                    <span className="text-gray-500 text-sm">技能：</span>
                    {employee.skills.map((skill, index) => (
                      <span
                        key={index}
                        className="bg-slate-700 text-cyan-300 px-2 py-1 rounded text-xs"
                      >
                        {skill}
                      </span>
                    ))}
                  </div>
                </div>
                
                <div className="flex gap-2 ml-4">
                  <button
                    onClick={() => setSelectedEmployee(employee)}
                    className="p-2 text-gray-400 hover:text-cyan-400 transition-colors"
                    title="查看详情"
                  >
                    <Icons.Eye className="w-4 h-4" />
                  </button>
                  <button
                    className="p-2 text-gray-400 hover:text-yellow-400 transition-colors"
                    title="调度"
                  >
                    <Icons.Calendar className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* 调度申请 */}
      {activeTab === 'requests' && (
        <div className="space-y-4">
          {scheduleRequests.map((request) => (
            <div
              key={request.id}
              className="bg-slate-800/50 border border-gray-600 rounded-lg p-4 hover:border-cyan-400/50 transition-colors"
            >
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-2">
                    <h3 className="text-lg font-semibold text-white">
                      {request.position} 调度申请
                    </h3>
                    <span className={`px-2 py-1 rounded-full text-xs text-white ${getRequestStatusColor(request.status)}`}>
                      {getRequestStatusText(request.status)}
                    </span>
                    <span className={`text-sm font-medium ${getUrgencyColor(request.urgency)}`}>
                      {getUrgencyText(request.urgency)}
                    </span>
                  </div>
                  
                  <p className="text-gray-300 mb-3">{request.reason}</p>
                  
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-400 mb-3">
                    <div>
                      <span className="text-gray-500">调出电站：</span>
                      <span className="text-white">{request.fromStationName}</span>
                    </div>
                    <div>
                      <span className="text-gray-500">调入电站：</span>
                      <span className="text-white">{request.toStationName}</span>
                    </div>
                    <div>
                      <span className="text-gray-500">开始时间：</span>
                      <span className="text-white">{request.startDate.toLocaleDateString()}</span>
                    </div>
                    <div>
                      <span className="text-gray-500">结束时间：</span>
                      <span className="text-white">{request.endDate.toLocaleDateString()}</span>
                    </div>
                  </div>

                  <div className="text-sm text-gray-400">
                    <span className="text-gray-500">申请人：</span>
                    <span className="text-white mr-4">{request.createdBy}</span>
                    <span className="text-gray-500">申请时间：</span>
                    <span className="text-white">{request.createdAt.toLocaleString()}</span>
                  </div>
                </div>
                
                {request.status === 'PENDING' && (
                  <div className="flex gap-2 ml-4">
                    <button className="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm transition-colors">
                      批准
                    </button>
                    <button className="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm transition-colors">
                      拒绝
                    </button>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      )}

      {scheduleRequests.length === 0 && activeTab === 'requests' && (
        <div className="text-center py-12">
          <Icons.Calendar className="w-16 h-16 text-gray-600 mx-auto mb-4" />
          <p className="text-gray-400">暂无调度申请</p>
        </div>
      )}

      {/* 新建调度模态框 */}
      {showScheduleModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold mb-4">新建调度申请</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">选择员工</label>
                <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                  <option value="">请选择员工</option>
                  {employees.map(emp => (
                    <option key={emp.id} value={emp.id}>{emp.name} - {emp.position}</option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">调度类型</label>
                <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                  <option value="">请选择调度类型</option>
                  {enumData?.dispatchType?.map(option => (
                    <option key={option.value} value={option.value}>{option.label}</option>
                  )) || [
                    <option key="temporary" value="temporary">临时调度</option>,
                    <option key="permanent" value="permanent">永久调度</option>,
                    <option key="emergency" value="emergency">紧急调度</option>
                  ]}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">目标电站</label>
                <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                  <option value="">请选择目标电站</option>
                  {stations.length > 0 ? stations.map(station => (
                    <option key={station.id} value={station.id}>{station.name}</option>
                  )) : [
                    <option key="station1" value="青海中控50MW">青海中控50MW</option>,
                    <option key="station2" value="青海中控10MW">青海中控10MW</option>,
                    <option key="station3" value="格尔木西勘院">格尔木西勘院</option>,
                    <option key="station4" value="吐鲁番浙火">吐鲁番浙火</option>
                  ]}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">紧急程度</label>
                <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                  <option value="">请选择紧急程度</option>
                  {enumData?.urgency?.map(option => (
                    <option key={option.value} value={option.value}>{option.label}</option>
                  )) || [
                    <option key="LOW" value="LOW">低</option>,
                    <option key="MEDIUM" value="MEDIUM">中</option>,
                    <option key="HIGH" value="HIGH">高</option>,
                    <option key="URGENT" value="URGENT">紧急</option>
                  ]}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">调度原因</label>
                <textarea
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                  rows={3}
                  placeholder="请输入调度原因..."
                />
              </div>
            </div>
            <div className="flex space-x-3 mt-6">
              <button
                onClick={() => {
                  // TODO: 实现创建调度申请的逻辑
                  console.log('创建调度申请');
                  setShowScheduleModal(false);
                }}
                className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700"
              >
                创建申请
              </button>
              <button
                onClick={() => setShowScheduleModal(false)}
                className="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-400"
              >
                取消
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default StaffScheduler;