import { Request, Response } from 'express';
import { ApiResponse } from '../types/index';
import { Employee } from '../models/Employee';
import { StationPosition } from '../models/StationPosition';
import { Position } from '../models/Position';
import { PowerStation } from '../models/PowerStation';
import { logger } from '../utils/logger';

// 获取所有外委员工
export const getAllExternalEmployees = async (req: Request, res: Response): Promise<void> => {
  try {
    const { stationId, positionId, status, page = 1, limit = 20 } = req.query;
    
    const filter: any = { employeeType: 'external' };
    if (stationId) filter.currentStationId = stationId;
    if (status) filter.status = status;
    
    const skip = (Number(page) - 1) * Number(limit);
    
    const [employees, total] = await Promise.all([
      Employee.find(filter)
        .populate('homeStationId', 'name')
        .populate('currentStationId', 'name')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(Number(limit)),
      Employee.countDocuments(filter)
    ]);
    
    // 如果指定了岗位ID，还需要获取岗位分配信息
    let employeesWithPositions = employees;
    if (positionId) {
      const employeeIds = employees.map(emp => emp._id);
      const stationPositions = await StationPosition.find({
        positionId,
        employeeId: { $in: employeeIds }
      }).populate('positionId', 'positionName department level');
      
      employeesWithPositions = employees.filter(emp => 
        stationPositions.some(sp => sp.employeeId && sp.employeeId.toString() === (emp._id as any).toString())
      );
    }
    
    const response: ApiResponse<{ records: typeof employeesWithPositions, pagination: any }> = {
      success: true,
      data: {
        records: employeesWithPositions,
        pagination: {
          current: Number(page),
          pageSize: Number(limit),
          total: employeesWithPositions.length,
          pages: Math.ceil(employeesWithPositions.length / Number(limit))
        }
      }
    };
    
    res.status(200).json(response);
  } catch (error) {
    logger.error('获取外委员工失败:', error);
    const response: ApiResponse = {
      success: false,
      message: '获取外委员工失败'
    };
    res.status(500).json(response);
  }
};

// 创建外委员工
export const createExternalEmployee = async (req: Request, res: Response): Promise<void> => {
  try {
    const employeeData = req.body;
    
    // 确保员工类型为外委
    employeeData.employeeType = 'external';
    
    // 如果没有指定currentStationId，默认设置为homeStationId
    if (!employeeData.currentStationId && employeeData.homeStationId) {
      employeeData.currentStationId = employeeData.homeStationId;
    }
    
    const employee = new Employee(employeeData);
    await employee.save();
    
    const populatedEmployee = await Employee.findById(employee._id)
      .populate('homeStationId', 'name')
      .populate('currentStationId', 'name');
    
    const response: ApiResponse<typeof populatedEmployee> = {
      success: true,
      message: '外委员工创建成功',
      data: populatedEmployee
    };
    
    res.status(201).json(response);
  } catch (error) {
    logger.error('创建外委员工失败:', error);
    const response: ApiResponse = {
      success: false,
      message: '创建外委员工失败'
    };
    res.status(500).json(response);
  }
};

// 分配外委员工到岗位
export const assignExternalEmployeeToPosition = async (req: Request, res: Response): Promise<void> => {
  try {
    const { employeeId, stationId, positionId, priority, notes } = req.body;
    
    // 验证员工、电站和岗位是否存在
    const [employee, station, position] = await Promise.all([
      Employee.findById(employeeId),
      PowerStation.findById(stationId),
      Position.findById(positionId)
    ]);
    
    if (!employee) {
      const response: ApiResponse = {
        success: false,
        message: '员工不存在'
      };
      res.status(404).json(response);
      return;
    }
    
    if (employee.employeeType !== 'external') {
      const response: ApiResponse = {
        success: false,
        message: '只能分配外委员工'
      };
      res.status(400).json(response);
      return;
    }
    
    if (!station) {
      const response: ApiResponse = {
        success: false,
        message: '电站不存在'
      };
      res.status(404).json(response);
      return;
    }
    
    if (!position) {
      const response: ApiResponse = {
        success: false,
        message: '岗位模板不存在'
      };
      res.status(404).json(response);
      return;
    }
    
    // 查找或创建电站岗位配置
    let stationPosition = await StationPosition.findOne({
      stationId,
      positionId
    });
    
    if (!stationPosition) {
      // 创建新的电站岗位配置
      stationPosition = new StationPosition({
        stationId,
        positionId,
        instance: 1,
        employeeId,
        status: 'occupied',
        priority: priority || 'medium',
        source: 'outsourcing',
        notes
      });
    } else {
      // 检查岗位是否已经分配给其他员工
      if (stationPosition.employeeId) {
        const response: ApiResponse = {
          success: false,
          message: '此岗位已分配给其他员工'
        };
        res.status(400).json(response);
        return;
      }
      
      // 分配员工到现有岗位
      stationPosition.employeeId = employeeId;
      stationPosition.status = 'occupied';
      stationPosition.updatedAt = new Date();
    }
    
    await stationPosition.save();
    
    // 更新员工的当前电站
    await Employee.findByIdAndUpdate(employeeId, {
      currentStationId: stationId
    });
    
    // 填充关联数据
    await stationPosition.populate([
      { path: 'stationId', select: 'name' },
      { path: 'positionId', select: 'positionName department level' },
      { path: 'employeeId', select: 'name employeeType status homeStationId currentStationId' }
    ]);
    
    const response: ApiResponse<typeof stationPosition> = {
      success: true,
      message: '外委员工分配成功',
      data: stationPosition
    };
    
    res.status(200).json(response);
  } catch (error) {
    logger.error('分配外委员工到岗位失败:', error);
    const response: ApiResponse = {
      success: false,
      message: '分配外委员工到岗位失败'
    };
    res.status(500).json(response);
  }
};

// 移除外委员工的岗位分配
export const removeExternalEmployeeFromPosition = async (req: Request, res: Response): Promise<void> => {
  try {
    const { employeeId, stationId, positionId } = req.body;
    
    const stationPosition = await StationPosition.findOne({
      stationId,
      positionId
    });
    
    if (!stationPosition) {
      const response: ApiResponse = {
        success: false,
        message: '岗位配置不存在'
      };
      res.status(404).json(response);
      return;
    }
    
    // 检查员工是否分配到此岗位
    if (!stationPosition.employeeId || stationPosition.employeeId.toString() !== employeeId) {
      const response: ApiResponse = {
        success: false,
        message: '员工未分配到此岗位'
      };
      res.status(400).json(response);
      return;
    }
    
    // 移除员工分配
    stationPosition.employeeId = undefined;
    stationPosition.status = 'vacant';
    stationPosition.updatedAt = new Date();
    
    await stationPosition.save();
    
    const response: ApiResponse = {
      success: true,
      message: '外委员工岗位分配已移除'
    };
    
    res.status(200).json(response);
  } catch (error) {
    logger.error('移除外委员工岗位分配失败:', error);
    const response: ApiResponse = {
      success: false,
      message: '移除外委员工岗位分配失败'
    };
    res.status(500).json(response);
  }
};

// 获取电站的外委员工
export const getStationExternalEmployees = async (req: Request, res: Response): Promise<void> => {
  try {
    const { stationId } = req.params;
    const { positionId, status = 'active' } = req.query;
    
    const filter: any = {
      currentStationId: stationId,
      employeeType: 'external',
      status
    };
    
    const employees = await Employee.find(filter)
      .populate('homeStationId', 'name')
      .populate('currentStationId', 'name');
    
    // 获取岗位分配信息
    const employeeIds = employees.map(emp => emp._id);
    let stationPositions = await StationPosition.find({
      stationId,
      employeeId: { $in: employeeIds }
    }).populate('positionId', 'positionName department level');
    
    if (positionId) {
      stationPositions = stationPositions.filter(sp => 
        sp.positionId._id.toString() === positionId
      );
    }
    
    // 组合员工和岗位信息
    const employeesWithPositions = employees.map(employee => {
      const positions = stationPositions.filter(sp => 
        sp.employeeId && sp.employeeId.toString() === (employee._id as any).toString()
      );
      return {
        ...employee.toObject(),
        assignedPositions: positions
      };
    });
    
    const response: ApiResponse<typeof employeesWithPositions> = {
      success: true,
      data: employeesWithPositions
    };
    
    res.status(200).json(response);
  } catch (error) {
    logger.error('获取电站外委员工失败:', error);
    const response: ApiResponse = {
      success: false,
      message: '获取电站外委员工失败'
    };
    res.status(500).json(response);
  }
};

// 获取外委员工的岗位分配
export const getExternalEmployeePositions = async (req: Request, res: Response): Promise<void> => {
  try {
    const { employeeId } = req.params;
    
    // 验证员工是否存在且为外委员工
    const employee = await Employee.findById(employeeId);
    if (!employee) {
      const response: ApiResponse = {
        success: false,
        message: '员工不存在'
      };
      res.status(404).json(response);
      return;
    }
    
    if (employee.employeeType !== 'external') {
      const response: ApiResponse = {
        success: false,
        message: '只能查询外委员工的岗位分配'
      };
      res.status(400).json(response);
      return;
    }
    
    // 获取岗位分配
    const stationPositions = await StationPosition.find({
      assignedEmployees: employeeId
    })
      .populate('stationId', 'name')
      .populate('positionId', 'positionName department level');
    
    const response: ApiResponse<typeof stationPositions> = {
      success: true,
      data: stationPositions
    };
    
    res.status(200).json(response);
  } catch (error) {
    logger.error('获取外委员工岗位分配失败:', error);
    const response: ApiResponse = {
      success: false,
      message: '获取外委员工岗位分配失败'
    };
    res.status(500).json(response);
  }
};

// 获取外委员工统计信息
export const getExternalEmployeeStats = async (req: Request, res: Response): Promise<void> => {
  try {
    const { stationId, startDate, endDate } = req.query;
    
    const dateFilter: any = {};
    if (startDate || endDate) {
      dateFilter.createdAt = {};
      if (startDate) dateFilter.createdAt.$gte = new Date(startDate as string);
      if (endDate) dateFilter.createdAt.$lte = new Date(endDate as string);
    }
    
    let matchCondition: any = { 
      employeeType: 'external',
      ...dateFilter
    };
    if (stationId) {
      matchCondition.currentStationId = stationId;
    }
    
    const [
      totalExternal,
      activeExternal,
      inactiveExternal,
      byStation,
      byStatus
    ] = await Promise.all([
      Employee.countDocuments(matchCondition),
      Employee.countDocuments({ ...matchCondition, status: 'active' }),
      Employee.countDocuments({ ...matchCondition, status: 'inactive' }),
      Employee.aggregate([
        { $match: matchCondition },
        { $lookup: { from: 'powerstations', localField: 'currentStationId', foreignField: '_id', as: 'station' } },
        { $unwind: '$station' },
        { $group: { _id: '$station.name', count: { $sum: 1 } } }
      ]),
      Employee.aggregate([
        { $match: matchCondition },
        { $group: { _id: '$status', count: { $sum: 1 } } }
      ])
    ]);
    
    // 获取岗位分配统计
    const positionStats = await StationPosition.aggregate([
      { $match: { personnelSource: 'external' } },
      { $lookup: { from: 'positions', localField: 'positionId', foreignField: '_id', as: 'position' } },
      { $unwind: '$position' },
      { $group: { _id: '$position.positionName', count: { $sum: 1 } } }
    ]);
    
    const stats = {
      total: totalExternal,
      active: activeExternal,
      inactive: inactiveExternal,
      byStation: byStation.reduce((acc: any, item: any) => {
        acc[item._id] = item.count;
        return acc;
      }, {}),
      byStatus: byStatus.reduce((acc: any, item: any) => {
        acc[item._id] = item.count;
        return acc;
      }, {}),
      byPosition: positionStats.reduce((acc: any, item: any) => {
        acc[item._id] = item.count;
        return acc;
      }, {})
    };
    
    const response: ApiResponse<typeof stats> = {
      success: true,
      data: stats
    };
    
    res.status(200).json(response);
  } catch (error) {
    logger.error('获取外委员工统计信息失败:', error);
    const response: ApiResponse = {
      success: false,
      message: '获取外委员工统计信息失败'
    };
    res.status(500).json(response);
  }
};

// 更新外委员工信息
export const updateExternalEmployee = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const updateData = req.body;
    
    // 确保不能修改员工类型
    delete updateData.employeeType;
    
    const employee = await Employee.findOneAndUpdate(
      { _id: id, employeeType: 'external' },
      { ...updateData, updatedAt: new Date() },
      { new: true, runValidators: true }
    ).populate('homeStationId', 'name')
     .populate('currentStationId', 'name');
    
    if (!employee) {
      const response: ApiResponse = {
        success: false,
        message: '外委员工不存在'
      };
      res.status(404).json(response);
      return;
    }
    
    const response: ApiResponse<typeof employee> = {
      success: true,
      message: '外委员工信息更新成功',
      data: employee
    };
    
    res.status(200).json(response);
  } catch (error) {
    logger.error('更新外委员工信息失败:', error);
    const response: ApiResponse = {
      success: false,
      message: '更新外委员工信息失败'
    };
    res.status(500).json(response);
  }
};

// 删除外委员工
export const deleteExternalEmployee = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    
    // 检查员工是否存在且为外委员工
    const employee = await Employee.findOne({ _id: id, employeeType: 'external' });
    
    if (!employee) {
      const response: ApiResponse = {
        success: false,
        message: '外委员工不存在'
      };
      res.status(404).json(response);
      return;
    }
    
    // 检查是否有岗位分配
    const hasPositions = await StationPosition.findOne({
      assignedEmployees: id
    });
    
    if (hasPositions) {
      const response: ApiResponse = {
        success: false,
        message: '员工仍有岗位分配，请先移除所有岗位分配'
      };
      res.status(400).json(response);
      return;
    }
    
    // 逻辑删除：将员工状态改为inactive
    await Employee.findByIdAndUpdate(id, {
      status: 'inactive',
      notes: `外委员工于${new Date().toISOString().split('T')[0]}离职`
    });
    
    const response: ApiResponse = {
      success: true,
      message: '外委员工已标记为离职状态'
    };
    
    res.status(200).json(response);
  } catch (error) {
    logger.error('删除外委员工失败:', error);
    const response: ApiResponse = {
      success: false,
      message: '删除外委员工失败'
    };
    res.status(500).json(response);
  }
};