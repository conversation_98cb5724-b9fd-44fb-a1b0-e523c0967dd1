import express from 'express';
import {
  getAllExternalEmployees,
  createExternalEmployee,
  assignExternalEmployeeToPosition,
  removeExternalEmployeeFromPosition,
  getStationExternalEmployees,
  getExternalEmployeePositions,
  getExternalEmployeeStats,
  updateExternalEmployee,
  deleteExternalEmployee
} from '../controllers/externalEmployeeController';

const router = express.Router();

// 获取所有外委员工
router.get('/', getAllExternalEmployees);

// 创建外委员工
router.post('/', createExternalEmployee);

// 更新外委员工信息
router.put('/:id', updateExternalEmployee);

// 删除外委员工
router.delete('/:id', deleteExternalEmployee);

// 分配外委员工到岗位
router.post('/assign', assignExternalEmployeeToPosition);

// 移除外委员工的岗位分配
router.post('/remove', removeExternalEmployeeFromPosition);

// 获取电站的外委员工
router.get('/station/:stationId', getStationExternalEmployees);

// 获取外委员工的岗位分配
router.get('/:employeeId/positions', getExternalEmployeePositions);

// 获取外委员工统计信息
router.get('/stats', getExternalEmployeeStats);

export default router;