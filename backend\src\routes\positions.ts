import express from 'express';
import {
  getAllPositions,
  getPositionsByDepartment,
  getStationPositions,
  getVacantPositions,
  assignEmployeeToPosition,
  removeEmployeeFromPosition,
  getStationPositionStats,
  getStationDetailedPositionStats
} from '../controllers/positionController';
import { authenticateToken } from '../middleware/auth';

const router = express.Router();

// 应用认证中间件
router.use(authenticateToken);

// 岗位模板相关路由
router.get('/templates', getAllPositions);
router.get('/templates/department/:department', getPositionsByDepartment);

// 电站岗位配置相关路由
router.get('/station/:stationId', getStationPositions);
router.get('/station/:stationId/vacant', getVacantPositions);
router.get('/station/:stationId/stats', getStationPositionStats);
router.get('/station/:stationId/detailed-stats', getStationDetailedPositionStats);

// 岗位分配相关路由
router.post('/assign', assignEmployeeToPosition);
router.delete('/assign/:stationPositionId/employee/:employeeId', removeEmployeeFromPosition);

export default router;