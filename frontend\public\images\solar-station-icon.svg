<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100">
  <defs>
    <!-- 发光效果 -->
    <filter id="glow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
    
    <!-- 渐变 -->
    <radialGradient id="solarGradient" cx="50%" cy="30%" r="70%">
      <stop offset="0%" style="stop-color:#fbbf24;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#f59e0b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#d97706;stop-opacity:1" />
    </radialGradient>
    
    <linearGradient id="panelGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1e40af;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#60a5fa;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 太阳 -->
  <circle cx="50" cy="25" r="12" fill="url(#solarGradient)" filter="url(#glow)"/>
  
  <!-- 太阳光线 -->
  <g stroke="#fbbf24" stroke-width="2" stroke-linecap="round" opacity="0.8">
    <line x1="50" y1="5" x2="50" y2="10"/>
    <line x1="65" y1="10" x2="62" y2="13"/>
    <line x1="70" y1="25" x2="65" y2="25"/>
    <line x1="65" y1="40" x2="62" y2="37"/>
    <line x1="50" y1="45" x2="50" y2="40"/>
    <line x1="35" y1="40" x2="38" y2="37"/>
    <line x1="30" y1="25" x2="35" y2="25"/>
    <line x1="35" y1="10" x2="38" y2="13"/>
  </g>
  
  <!-- 太阳能板支架 -->
  <rect x="20" y="50" width="60" height="4" fill="#64748b" rx="2"/>
  
  <!-- 太阳能板 -->
  <g>
    <!-- 主面板 -->
    <rect x="15" y="45" width="70" height="25" fill="url(#panelGradient)" rx="2" stroke="#1e40af" stroke-width="1"/>
    
    <!-- 面板分割线 -->
    <g stroke="#1e40af" stroke-width="0.5" opacity="0.7">
      <line x1="25" y1="45" x2="25" y2="70"/>
      <line x1="35" y1="45" x2="35" y2="70"/>
      <line x1="45" y1="45" x2="45" y2="70"/>
      <line x1="55" y1="45" x2="55" y2="70"/>
      <line x1="65" y1="45" x2="65" y2="70"/>
      <line x1="75" y1="45" x2="75" y2="70"/>
      
      <line x1="15" y1="52" x2="85" y2="52"/>
      <line x1="15" y1="57" x2="85" y2="57"/>
      <line x1="15" y1="62" x2="85" y2="62"/>
    </g>
  </g>
  
  <!-- 支撑塔 -->
  <rect x="48" y="70" width="4" height="20" fill="#64748b"/>
  
  <!-- 基座 -->
  <rect x="40" y="88" width="20" height="8" fill="#475569" rx="2"/>
  
  <!-- 电力传输线 -->
  <g stroke="#06b6d4" stroke-width="1" fill="none" opacity="0.6">
    <path d="M85 57 Q90 55 95 50" stroke-dasharray="2,2"/>
    <circle cx="95" cy="50" r="1" fill="#06b6d4"/>
  </g>
  
  <!-- 状态指示灯 -->
  <circle cx="80" cy="48" r="2" fill="#10b981" opacity="0.8">
    <animate attributeName="opacity" values="0.8;0.3;0.8" dur="2s" repeatCount="indefinite"/>
  </circle>
</svg>