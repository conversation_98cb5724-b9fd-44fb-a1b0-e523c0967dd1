import mongoose from 'mongoose';
import { User } from '../models/User';

const connectDB = async () => {
  try {
    await mongoose.connect('mongodb://localhost:27017/power_station_management');
    console.log('✅ MongoDB 连接成功');
  } catch (error) {
    console.error('❌ MongoDB 连接失败:', error);
    process.exit(1);
  }
};

const createDefaultUser = async () => {
  console.log('🚀 开始创建默认用户...');
  
  await connectDB();
  
  try {
    // 检查是否已存在管理员用户
    const existingAdmin = await User.findOne({ username: 'admin' });
    
    if (existingAdmin) {
      console.log('⚠️ 管理员用户已存在，跳过创建');
    } else {
      // 创建默认管理员用户
      const adminUser = new User({
        username: 'admin',
        email: '<EMAIL>',
        password: 'admin123', // 密码会在保存时自动加密
        role: 'admin',
        permissions: ['all'],
        isActive: true
      });
      
      await adminUser.save();
      console.log('✅ 创建默认管理员用户: admin / admin123');
    }
    
    // 检查是否已存在操作员用户
    const existingOperator = await User.findOne({ username: 'operator' });
    
    if (existingOperator) {
      console.log('⚠️ 操作员用户已存在，跳过创建');
    } else {
      // 创建默认操作员用户
      const operatorUser = new User({
        username: 'operator',
        email: '<EMAIL>',
        password: 'operator123',
        role: 'operator',
        permissions: ['read', 'write'],
        isActive: true
      });
      
      await operatorUser.save();
      console.log('✅ 创建默认操作员用户: operator / operator123');
    }
    
    // 检查是否已存在查看者用户
    const existingViewer = await User.findOne({ username: 'viewer' });
    
    if (existingViewer) {
      console.log('⚠️ 查看者用户已存在，跳过创建');
    } else {
      // 创建默认查看者用户
      const viewerUser = new User({
        username: 'viewer',
        email: '<EMAIL>',
        password: 'viewer123',
        role: 'viewer',
        permissions: ['read'],
        isActive: true
      });
      
      await viewerUser.save();
      console.log('✅ 创建默认查看者用户: viewer / viewer123');
    }
    
    // 统计用户数量
    const userCount = await User.countDocuments();
    console.log(`\n📊 用户统计: 共 ${userCount} 个用户`);
    
    console.log('\n🎉 默认用户创建完成！');
    console.log('默认账号信息:');
    console.log('  管理员: admin / admin123');
    console.log('  操作员: operator / operator123');
    console.log('  查看者: viewer / viewer123');
    
  } catch (error) {
    console.error('❌ 创建默认用户失败:', error);
  } finally {
    await mongoose.disconnect();
    console.log('✅ 数据库连接已关闭');
  }
};

// 执行创建默认用户
if (require.main === module) {
  createDefaultUser().catch(console.error);
}

export { createDefaultUser };