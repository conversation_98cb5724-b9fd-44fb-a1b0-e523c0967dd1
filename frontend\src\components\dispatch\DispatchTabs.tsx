import React from 'react';
import { Icons } from '../../constants/index';

interface DispatchTabsProps {
  activeTab: 'overview' | 'planning' | 'approval';
  onTabChange: (tab: 'overview' | 'planning' | 'approval') => void;
}

const DispatchTabs: React.FC<DispatchTabsProps> = ({ activeTab, onTabChange }) => {
  const tabs = [
    { key: 'overview', label: '调度看板', icon: Icons.Dashboard },
    { key: 'planning', label: '调度计划', icon: Icons.Calendar },
    { key: 'approval', label: '申请审批', icon: Icons.Eye }
  ] as const;

  return (
    <div className="flex space-x-2" style={{zIndex: 10, position: 'relative'}}>
      {tabs.map(({ key, label, icon: Icon }) => (
        <button
          key={key}
          onClick={() => {
            console.log(`🔄 点击${label}标签`);
            onTabChange(key);
            console.log(`✅ 设置activeTab为${key}`);
          }}
          className={`px-4 py-2 rounded-lg font-medium transition-colors ${
            activeTab === key
              ? 'bg-blue-600 text-white'
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          }`}
        >
          <Icon className="w-4 h-4 inline mr-2" />
          {label}
        </button>
      ))}
    </div>
  );
};

export default DispatchTabs;