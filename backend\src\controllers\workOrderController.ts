import { Request, Response } from 'express';
import WorkOrder from '../models/WorkOrder';
import { PowerStation } from '../models/PowerStation';

// 获取所有工单
export const getWorkOrders = async (req: Request, res: Response) => {
  try {
    const {
      page = 1,
      limit = 50,
      status,
      stationId,
      priority,
      type,
      assignedTo
    } = req.query;

    // 构建查询条件
    const query: any = {};
    
    if (status) query.status = status;
    if (stationId) query.stationId = stationId;
    if (priority) query.priority = priority;
    if (type) query.type = type;
    if (assignedTo) query.assignedTo = assignedTo;

    const skip = (Number(page) - 1) * Number(limit);

    const workOrders = await WorkOrder.find(query)
      .populate('stationId', 'name address')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(Number(limit));

    const total = await WorkOrder.countDocuments(query);

    res.json({
      success: true,
      data: workOrders,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        pages: Math.ceil(total / Number(limit))
      }
    });
  } catch (error) {
    console.error('获取工单失败:', error);
    res.status(500).json({
      success: false,
      message: '获取工单失败',
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
};

// 根据ID获取工单
export const getWorkOrderById = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;

    const workOrder = await WorkOrder.findById(id)
      .populate('stationId', 'name address');

    if (!workOrder) {
      res.status(404).json({
        success: false,
        message: '工单不存在'
      });
      return;
    }

    res.json({
      success: true,
      data: workOrder
    });
  } catch (error) {
    console.error('获取工单详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取工单详情失败',
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
};

// 创建工单
export const createWorkOrder = async (req: Request, res: Response): Promise<void> => {
  try {
    const {
      title,
      description,
      stationId,
      assignedTo,
      priority = 'MEDIUM',
      type,
      estimatedDuration,
      notes
    } = req.body;

    // 验证电站是否存在
    const station = await PowerStation.findById(stationId);
    if (!station) {
      res.status(400).json({
        success: false,
        message: '指定的电站不存在'
      });
      return;
    }

    const workOrder = new WorkOrder({
      title,
      description,
      stationId,
      createdBy: (req as any).user?.username || 'system',
      assignedTo,
      priority,
      type,
      estimatedDuration,
      notes
    });

    await workOrder.save();

    // 填充关联数据
    await workOrder.populate('stationId', 'name address');

    res.status(201).json({
      success: true,
      data: workOrder,
      message: '工单创建成功'
    });
  } catch (error) {
    console.error('创建工单失败:', error);
    res.status(500).json({
      success: false,
      message: '创建工单失败',
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
};

// 更新工单
export const updateWorkOrder = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    // 如果状态更新为完成，设置完成时间
    if (updateData.status === 'COMPLETED' && !updateData.completedAt) {
      updateData.completedAt = new Date();
    }

    const workOrder = await WorkOrder.findByIdAndUpdate(
      id,
      updateData,
      { new: true, runValidators: true }
    ).populate('stationId', 'name address');

    if (!workOrder) {
      res.status(404).json({
        success: false,
        message: '工单不存在'
      });
      return;
    }

    res.json({
      success: true,
      data: workOrder,
      message: '工单更新成功'
    });
  } catch (error) {
    console.error('更新工单失败:', error);
    res.status(500).json({
      success: false,
      message: '更新工单失败',
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
};

// 删除工单
export const deleteWorkOrder = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;

    const workOrder = await WorkOrder.findByIdAndDelete(id);

    if (!workOrder) {
      res.status(404).json({
        success: false,
        message: '工单不存在'
      });
      return;
    }

    res.json({
      success: true,
      message: '工单删除成功'
    });
  } catch (error) {
    console.error('删除工单失败:', error);
    res.status(500).json({
      success: false,
      message: '删除工单失败',
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
};

// 获取工单统计
export const getWorkOrderStats = async (req: Request, res: Response) => {
  try {
    const stats = await WorkOrder.aggregate([
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 }
        }
      }
    ]);

    const priorityStats = await WorkOrder.aggregate([
      {
        $group: {
          _id: '$priority',
          count: { $sum: 1 }
        }
      }
    ]);

    const typeStats = await WorkOrder.aggregate([
      {
        $group: {
          _id: '$type',
          count: { $sum: 1 }
        }
      }
    ]);

    res.json({
      success: true,
      data: {
        statusStats: stats,
        priorityStats,
        typeStats
      }
    });
  } catch (error) {
    console.error('获取工单统计失败:', error);
    res.status(500).json({
      success: false,
      message: '获取工单统计失败',
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
};
