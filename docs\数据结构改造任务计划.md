# 电站人员调度系统数据结构改造任务计划

## 改造概述
基于新的种子数据设计（docs/电站-员工种子数据.md），对系统进行底层数据结构重构，实现电站、岗位、员工信息、调度和兼职、外委的清晰分离和合理组织。

## 新数据结构特点
1. **电站信息独立化**：电站基础信息与人员配置分离
2. **岗位模板化**：标准化岗位定义，支持多电站复用
3. **员工类型细分**：full_time、intern、external、borrowed等明确分类
4. **调度记录规范化**：明确的调度来源、目标、状态跟踪
5. **兼职岗位支持**：支持员工在多个电站兼职
6. **外委管理**：外委公司和人员的规范管理

## 任务清单

### 阶段一：数据模型重构 ✅ **已完成**
- [x] 1.1 创建新的Position模型（岗位模板）
- [x] 1.2 创建StationPosition模型（电站岗位配置）
- [x] 1.3 创建ConcurrentPosition模型（兼职岗位）
- [x] 1.4 更新Employee模型，适配新的员工类型
- [x] 1.5 更新PowerStation模型，移除人员相关字段
- [x] 1.6 更新DispatchRecord模型，支持新的调度类型
- [x] 1.7 更新models/index.ts导出新模型
- [x] 1.8 创建新的数据库初始化脚本
- [x] 1.9 测试新数据结构的数据库初始化

### 阶段二：数据迁移和种子数据 ✅ **已完成**
- [x] 2.1 创建新的种子数据脚本（initNewDatabase.ts）
- [x] 2.2 实现数据库初始化（已成功初始化270个员工、8个电站、29个岗位模板等）
- [x] 2.3 删除旧的种子数据文件和脚本
- [x] 2.4 清理重复和冲突的脚本文件

### 阶段三：API接口更新 ✅ **已完成**
- [x] 3.1 更新员工相关API接口（employeeController.ts）
  - [x] getAllEmployees - 获取所有员工
  - [x] getEmployeePositions - 获取员工岗位信息
  - [x] getEmployeeWorkHistory - 获取员工工作历史
  - [x] getEmployeesByType - 按类型获取员工
  - [x] getEmployeeStats - 获取员工统计信息
  - [x] createEmployee - 创建员工
  - [x] updateEmployee - 更新员工信息
  - [x] deleteEmployee - 删除员工（逻辑删除）
  - [x] getEmployeesByStation - 按电站获取员工
  - [x] getCurrentEmployeesByStation - 获取电站当前员工
  - [x] getHomeEmployeesByStation - 获取电站归属员工
  - [x] getDispatchedInEmployees - 获取调入员工
  - [x] getDispatchedOutEmployees - 获取调出员工
  - [x] getStationDispatchOverview - 获取电站调度概览

- [x] 3.2 更新电站相关API接口（stationController.ts）
  - [x] 电站详情API增加岗位配置信息
  - [x] 删除电站时清理相关岗位配置
  - [x] getStationPersonnelOverview - 获取电站人员配置概览
  - [x] getStationVacancies - 获取电站岗位空缺情况

- [x] 3.3 新增岗位管理API接口（positionController.ts）
  - [x] getAllPositions - 获取所有岗位模板
  - [x] getPositionsByDepartment - 按部门获取岗位
  - [x] getStationPositions - 获取电站岗位配置
  - [x] getVacantPositions - 获取空缺岗位
  - [x] assignEmployeeToPosition - 分配员工到岗位
  - [x] removeEmployeeFromPosition - 从岗位移除员工
  - [x] getStationPositionStats - 获取电站岗位统计

- [x] 3.4 更新调度相关API接口（dispatchController.ts）
  - [x] getDispatchRecords - 获取调度记录
  - [x] createDispatchRecord - 创建调度记录
  - [x] createBatchDispatchRecords - 批量创建调度记录
  - [x] aiQueryEmployees - AI员工查询
  - [x] getAIDispatchRecommendations - AI调度推荐
  - [x] reviewDispatchRecord - 调度记录审核
  - [x] getDispatchStatistics - 调度统计

- [x] 3.5 新增兼职管理API接口（concurrentPositionController.ts）
  - [x] getAllConcurrentPositions - 获取所有兼职记录
  - [x] createConcurrentPosition - 创建兼职记录
  - [x] updateConcurrentPosition - 更新兼职记录
  - [x] endConcurrentPosition - 结束兼职
  - [x] getEmployeeConcurrentPositions - 获取员工兼职记录
  - [x] getStationConcurrentEmployees - 获取电站兼职员工
  - [x] getPositionConcurrentEmployees - 获取岗位兼职员工
  - [x] getConcurrentPositionStats - 获取兼职统计信息
  - [x] deleteConcurrentPosition - 删除兼职记录

- [x] 3.6 新增外委管理API接口（externalEmployeeController.ts）
  - [x] getAllExternalEmployees - 获取所有外委员工
  - [x] createExternalEmployee - 创建外委员工
  - [x] updateExternalEmployee - 更新外委员工信息
  - [x] deleteExternalEmployee - 删除外委员工
  - [x] assignExternalEmployeeToPosition - 分配外委员工到岗位
  - [x] removeExternalEmployeeFromPosition - 移除外委员工岗位分配
  - [x] getStationExternalEmployees - 获取电站外委员工
  - [x] getExternalEmployeePositions - 获取外委员工岗位分配
  - [x] getExternalEmployeeStats - 获取外委员工统计信息

### 阶段四：路由配置更新 ✅ **已完成**
- [x] 4.1 更新路由索引文件（routes/index.ts）
- [x] 4.2 创建兼职管理路由（routes/concurrentPositions.ts）
- [x] 4.3 创建外委管理路由（routes/externalEmployees.ts）
- [x] 4.4 清理重复的路由配置

### 阶段五：代码冲突解决 ✅ **已完成**
- [x] 5.1 解决兼职管理功能重复问题
  - [x] 删除positionController中重复的兼职函数
  - [x] 删除positions路由中重复的兼职路由
  - [x] 统一使用concurrentPositionController处理兼职业务
- [x] 5.2 删除重复的种子数据脚本
- [x] 5.3 确保模型导出的一致性

### 阶段六：数据库验证 ✅ **已完成**
- [x] 6.1 验证数据库初始化成功
  - [x] 电站数量: 8
  - [x] 岗位模板: 29
  - [x] 员工数量: 270
  - [x] 岗位配置: 10
  - [x] 兼职岗位: 20
- [x] 6.2 创建数据库检查脚本（checkDatabase.ts）

### 阶段七：前端适配 ⏳ **待开始**
- [ ] 7.1 更新员工管理页面
- [ ] 7.2 更新电站管理页面
- [ ] 7.3 新增岗位管理页面
- [ ] 7.4 更新调度管理页面
- [ ] 7.5 新增兼职管理功能
- [ ] 7.6 新增外委管理功能

### 阶段八：测试和优化 ⏳ **待开始**
- [ ] 8.1 单元测试更新
- [ ] 8.2 集成测试
- [ ] 8.3 性能优化
- [ ] 8.4 文档更新

## 详细任务说明

### 1. 数据模型设计

#### 1.1 Position模型（岗位模板）
```typescript
interface IPosition {
  _id: string;
  positionName: string;        // 岗位名称
  department: string;          // 所属部门
  skillRequirements: string[]; // 技能要求
  certificationRequirements: string[]; // 证书要求
  description?: string;        // 岗位描述
  level: 'junior' | 'senior' | 'expert'; // 岗位级别
}
```

#### 1.2 StationPosition模型（电站岗位配置）
```typescript
interface IStationPosition {
  _id: string;
  stationId: string;          // 电站ID
  positionId: string;         // 岗位模板ID
  instance: number;           // 岗位实例编号（同一岗位可能有多个）
  employeeId?: string;        // 当前员工ID
  status: 'occupied' | 'vacant' | 'not_configured'; // 配置状态
  priority: 'high' | 'medium' | 'low'; // 优先级
  notes?: string;             // 备注
}
```

#### 1.3 ConcurrentPosition模型（兼职岗位）
```typescript
interface IConcurrentPosition {
  _id: string;
  employeeId: string;         // 员工ID
  stationId: string;          // 兼职电站ID
  positionId: string;         // 兼职岗位ID
  startDate: Date;            // 开始日期
  endDate?: Date;             // 结束日期
  workload: number;           // 工作量百分比
  status: 'active' | 'inactive'; // 状态
}
```

### 2. 已删除的文件
- ✅ `backend/dist/scripts/seedStationPersonnelData.js`（及相关编译产物）
- ✅ `backend/src/scripts/seedNewData.ts`（重复文件）

### 3. 数据保留策略
- ✅ 保留电站的经纬度坐标信息
- ✅ 保留员工的技能和证书信息
- ✅ 保留现有的调度记录（已适配新模型）
- ✅ 保留发电量等业务数据

## 当前系统状态

### 数据库状态 ✅
- MongoDB连接正常
- 数据初始化完成
- 包含完整的电站、员工、岗位、兼职数据

### API接口状态 ✅
- 所有控制器已更新
- 路由配置完整
- 无重复和冲突的代码

### 模型状态 ✅
- 所有新模型已创建
- 模型关联关系正确
- 索引配置完整

## 风险评估
1. ✅ **数据一致性风险**：已通过数据库验证确保数据完整性
2. ⚠️ **API兼容性风险**：前端可能需要适配工作（待处理）
3. ✅ **业务连续性风险**：后端API已完全重构，功能完整

## 实施建议
1. ✅ 已在开发环境完成所有后端改造
2. ✅ 已分阶段提交，系统可正常运行
3. ✅ 数据库已成功初始化，包含完整数据
4. ⏳ 下一步：前端适配和测试

## 预期收益
1. ✅ 数据结构更加清晰和规范
2. ✅ 支持更复杂的人员调度场景（兼职、外委）
3. ✅ 便于后续功能扩展
4. ✅ 提高系统维护性和可读性

## 总结
**后端数据结构改造已全部完成！** 包括：
- 数据模型重构
- 数据库初始化
- API接口更新
- 路由配置
- 代码冲突解决

下一步工作重点：前端页面适配新的API接口。