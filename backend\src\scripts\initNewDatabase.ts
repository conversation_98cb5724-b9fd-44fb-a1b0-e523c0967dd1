import mongoose from 'mongoose';
import { PowerStation, Employee, Position, StationPosition, ConcurrentPosition } from '../models';
import DispatchRecord, { DispatchType, DispatchStatus, DispatchSource } from '../models/DispatchRecord';
import fs from 'fs';
import path from 'path';

// 连接数据库
const connectDB = async () => {
  try {
    await mongoose.connect('mongodb://localhost:27017/power_station_management');
    console.log('✅ MongoDB 连接成功');
  } catch (error) {
    console.error('❌ MongoDB 连接失败:', error);
    process.exit(1);
  }
};

// 读取新的种子数据
const loadNewSeedData = () => {
  try {
    const filePath = path.join(__dirname, '../../../docs/电站-员工种子数据.md');
    const content = fs.readFileSync(filePath, 'utf-8');
    // 直接解析JSON内容
    const seedData = JSON.parse(content);
    console.log('✅ 成功读取新种子数据文件');
    return seedData;
  } catch (error) {
    console.error('❌ 读取新种子数据文件失败:', error);
    console.error('错误详情:', error instanceof Error ? error.message : String(error));
    process.exit(1);
  }
};

// 部门映射
const getDepartmentByPosition = (positionName: string): string => {
  const departmentMap: { [key: string]: string } = {
    '项目经理': 'management',
    '项目副经理': 'management',
    '生产部经理': 'management',
    '设备部经理': 'management',
    '安全专工': 'safety',
    '安全员': 'safety',
    '值长': 'production',
    '聚光集热专工': 'production',
    '储换热专工': 'production',
    '电气专工': 'production',
    '热控/DCS专工': 'production',
    '化水专工': 'production',
    '汽机专工': 'production',
    '聚光集热主操': 'production',
    '储换热主操': 'production',
    '电气主操': 'production',
    '汽机主操': 'production',
    '化水主操': 'production',
    '副操兼巡检': 'production',
    '镜场专工': 'equipment',
    '镜场检修维护班长': 'equipment',
    '镜场检修': 'equipment',
    '镜场清洗': 'equipment',
    '热力机械检修班长': 'equipment',
    '机务检修（高压焊工）': 'equipment',
    '机务检修': 'equipment',
    '设备管理员': 'equipment',
    '电仪检修班长': 'equipment',
    '电仪检修': 'equipment'
  };
  return departmentMap[positionName] || 'production';
};

// 获取岗位级别
const getPositionLevel = (positionName: string): 'junior' | 'senior' | 'expert' => {
  if (positionName.includes('经理') || positionName.includes('专工')) {
    return 'expert';
  }
  if (positionName.includes('班长') || positionName.includes('主操')) {
    return 'senior';
  }
  return 'junior';
};

// 初始化新数据库
const initNewDatabase = async () => {
  console.log('🚀 开始初始化新数据结构数据库...');
  
  await connectDB();
  
  // 清空现有数据
  console.log('🧹 清空现有数据...');
  await Promise.all([
    Employee.deleteMany({}),
    PowerStation.deleteMany({}),
    Position.deleteMany({}),
    StationPosition.deleteMany({}),
    ConcurrentPosition.deleteMany({}),
    DispatchRecord.deleteMany({})
  ]);
  
  const seedData = loadNewSeedData();
  
  // 1. 创建电站
  console.log('🏭 创建电站数据...');
  const stationMap = new Map();
  
  for (const stationData of seedData.powerStations) {
    // 为电站提供默认位置信息
    const defaultLocation = {
      province: '青海省',
      city: '德令哈市',
      address: '德令哈工业园区',
      coordinates: {
        latitude: 37.0 + Math.random() * 2,
        longitude: 97.0 + Math.random() * 2
      }
    };
    
    // 根据电站名称调整位置
    if (stationData.name.includes('吐鲁番') || stationData.name.includes('鄯善') || stationData.name.includes('新疆')) {
      defaultLocation.province = '新疆维吾尔自治区';
      defaultLocation.city = stationData.name.includes('吐鲁番') ? '吐鲁番市' : 
                            stationData.name.includes('鄯善') ? '鄯善县' : '博州';
      defaultLocation.coordinates.latitude = 42.0 + Math.random() * 2;
      defaultLocation.coordinates.longitude = 89.0 + Math.random() * 2;
    } else if (stationData.name.includes('金塔')) {
      defaultLocation.province = '甘肃省';
      defaultLocation.city = '金塔县';
      defaultLocation.coordinates.latitude = 40.0 + Math.random() * 2;
      defaultLocation.coordinates.longitude = 98.0 + Math.random() * 2;
    }
    
    const station = new PowerStation({
      name: stationData.name,
      location: stationData.location || defaultLocation,
      type: 'solar', // 默认为太阳能
      capacity: stationData.name.includes('50MW') ? 50 : 10, // 从名称推断容量
      commissionDate: new Date('2020-01-01'),
      status: 'active'
    });
    
    const savedStation = await station.save();
    stationMap.set(stationData.name, savedStation);
    console.log(`  ✅ 创建电站: ${stationData.name}`);
  }
  
  // 2. 创建岗位模板
  console.log('📋 创建岗位模板...');
  const positionMap = new Map();
  
  for (const positionTemplate of seedData.positionTemplates) {
    const position = new Position({
      positionName: positionTemplate.positionName,
      department: positionTemplate.department,
      level: getPositionLevel(positionTemplate.positionName),
      skillRequirements: [], // 可以后续添加
      certificationRequirements: [], // 可以后续添加
      isActive: true
    });
    
    const savedPosition = await position.save();
    positionMap.set(positionTemplate.positionName, savedPosition);
    console.log(`  ✅ 创建岗位模板: ${positionTemplate.positionName}`);
  }
  
  // 3. 创建员工
  console.log('👥 创建员工数据...');
  const employeeMap = new Map();
  
  for (const employeeData of seedData.employees) {
    // 根据种子数据中的homeStationName分配归属电站
    const homeStation = stationMap.get(employeeData.homeStationName);
    if (!homeStation) {
      console.warn(`  ⚠️  未找到归属电站: ${employeeData.homeStationName} (员工: ${employeeData.name})`);
      continue;
    }
    
    const employee = new Employee({
      name: employeeData.name,
      homeStationId: homeStation._id,
      currentStationId: homeStation._id, // 初始时当前电站等于归属电站
      employeeType: employeeData.employeeType,
      status: employeeData.employeeType === 'intern' ? 'learning' : 'active',
      hireDate: new Date('2020-01-01'),
      skills: [],
      certifications: [],
      notes: employeeData.notes || '',
      externalCompany: employeeData.externalCompany || ''
    });
    
    const savedEmployee = await employee.save();
    employeeMap.set(employeeData.name, savedEmployee);
    console.log(`  ✅ 创建员工: ${employeeData.name} (${employeeData.employeeType}) - 归属: ${employeeData.homeStationName}`);
  }
  
  // 4. 为员工创建主岗位配置
  console.log('🎯 为员工创建主岗位配置...');
  
  // 用于跟踪每个电站-岗位组合的实例计数
  const positionInstanceMap = new Map<string, number>();
  
  for (const employeeData of seedData.employees) {
    const employee = employeeMap.get(employeeData.name);
    const homeStation = stationMap.get(employeeData.homeStationName);
    const primaryPosition = positionMap.get(employeeData.primaryPositionName);
    
    if (employee && homeStation && primaryPosition) {
      // 生成唯一键用于跟踪实例
      const positionKey = `${homeStation._id}-${primaryPosition._id}`;
      
      // 获取或初始化该岗位的实例计数
      const currentInstance = (positionInstanceMap.get(positionKey) || 0) + 1;
      positionInstanceMap.set(positionKey, currentInstance);
      
      const stationPosition = new StationPosition({
        stationId: homeStation._id,
        positionId: primaryPosition._id,
        instance: currentInstance, // 使用递增的实例编号
        employeeId: employee._id,
        status: 'occupied',
        priority: 'high',
        notes: `主岗位 - ${employeeData.notes || ''}`.trim()
      });
      
      await stationPosition.save();
      console.log(`  ✅ 主岗位: ${employeeData.name} - ${employeeData.homeStationName} - ${employeeData.primaryPositionName} (实例${currentInstance})`);
    } else {
      if (!employee) console.warn(`  ⚠️  未找到员工: ${employeeData.name}`);
      if (!homeStation) console.warn(`  ⚠️  未找到电站: ${employeeData.homeStationName}`);
      if (!primaryPosition) console.warn(`  ⚠️  未找到岗位: ${employeeData.primaryPositionName}`);
    }
  }

  // 5. 创建额外的电站岗位配置（来自staffing数据）
  console.log('🔧 创建额外的电站岗位配置...');
  
  for (const staffingData of seedData.staffing) {
    const station = stationMap.get(staffingData.stationName);
    const position = positionMap.get(staffingData.positionName);
    const employee = staffingData.employeeName ? employeeMap.get(staffingData.employeeName) : null;
    
    if (station && position) {
      // 生成唯一键用于跟踪实例
      const positionKey = `${station._id}-${position._id}`;
      
      // 获取或初始化该岗位的实例计数
      const currentInstance = (positionInstanceMap.get(positionKey) || 0) + 1;
      positionInstanceMap.set(positionKey, currentInstance);
      
      const stationPosition = new StationPosition({
        stationId: station._id,
        positionId: position._id,
        instance: currentInstance, // 使用递增的实例编号
        employeeId: employee?._id,
        status: staffingData.status === 'occupied' ? 'occupied' : 'vacant',
        priority: 'medium',
        notes: staffingData.notes || ''
      });
      
      await stationPosition.save();
      console.log(`  ✅ 配置岗位: ${staffingData.stationName} - ${staffingData.positionName} (实例${currentInstance})`);
    }
  }
  
  // 6. 创建兼职岗位
  console.log('🔄 创建兼职岗位...');
  
  for (const concurrentData of seedData.concurrentPositions) {
    const employee = employeeMap.get(concurrentData.employeeName);
    const station = stationMap.get(concurrentData.stationName);
    const position = positionMap.get(concurrentData.positionName);
    
    if (employee && station && position) {
      const concurrentPosition = new ConcurrentPosition({
        employeeId: employee._id,
        stationId: station._id,
        positionId: position._id,
        startDate: new Date('2024-01-01'),
        workload: 30, // 默认30%工作量
        status: 'active'
      });
      
      await concurrentPosition.save();
      console.log(`  ✅ 创建兼职: ${concurrentData.employeeName} 在 ${concurrentData.stationName} 兼任 ${concurrentData.positionName}`);
    }
  }
  
  // 7. 处理调度记录（创建调度记录并更新员工当前电站）
  console.log('📋 处理调度记录...');
  
  for (const dispatchData of seedData.dispatchRecords) {
    const employee = employeeMap.get(dispatchData.employeeName);
    const fromStation = stationMap.get(dispatchData.fromStation);
    const toStation = stationMap.get(dispatchData.toStation);
    
    if (employee && fromStation && toStation) {
      // 创建调度记录
      const dispatchRecord = new DispatchRecord({
        employeeId: employee._id,
        fromStationId: fromStation._id,
        toStationId: toStation._id,
        dispatchType: DispatchType.TEMPORARY, // 根据备注判断，大部分是借调
        startDate: new Date('2024-01-01'), // 默认开始日期
        endDate: new Date('2024-12-31'), // 默认结束日期，临时调度
        reason: dispatchData.notes || '人员调配',
        status: DispatchStatus.ACTIVE, // 设置为执行中状态
        source: DispatchSource.MANUAL,
        isBatch: false
      });
      
      await dispatchRecord.save();
      
      // 更新员工当前电站
      await Employee.findByIdAndUpdate(employee._id, {
        currentStationId: toStation._id,
        notes: `${employee.notes || ''} ${dispatchData.notes || ''}`.trim()
      });
      
      console.log(`  ✅ 调度记录: ${dispatchData.employeeName} 从 ${dispatchData.fromStation} 到 ${dispatchData.toStation} (${dispatchData.notes || ''})`);
    } else {
      if (!employee) console.warn(`  ⚠️  未找到员工: ${dispatchData.employeeName}`);
      if (!fromStation) console.warn(`  ⚠️  未找到调出电站: ${dispatchData.fromStation}`);
      if (!toStation) console.warn(`  ⚠️  未找到调入电站: ${dispatchData.toStation}`);
    }
  }
  
  // 统计信息
  const stats = {
    powerStations: await PowerStation.countDocuments(),
    positions: await Position.countDocuments(),
    employees: await Employee.countDocuments(),
    stationPositions: await StationPosition.countDocuments(),
    concurrentPositions: await ConcurrentPosition.countDocuments(),
    dispatchRecords: await DispatchRecord.countDocuments()
  };
  
  console.log('\n📊 数据库初始化完成！');
  console.log('统计信息:');
  console.log(`  电站数量: ${stats.powerStations}`);
  console.log(`  岗位模板: ${stats.positions}`);
  console.log(`  员工数量: ${stats.employees}`);
  console.log(`  岗位配置: ${stats.stationPositions}`);
  console.log(`  兼职岗位: ${stats.concurrentPositions}`);
  console.log(`  调度记录: ${stats.dispatchRecords}`);
  
  await mongoose.disconnect();
  console.log('✅ 数据库连接已关闭');
};

// 执行初始化
if (require.main === module) {
  initNewDatabase().catch(console.error);
}

export { initNewDatabase };