import mongoose, { Schema, Document, Types } from 'mongoose';

// 兼职岗位接口
export interface IConcurrentPosition {
  _id?: string;
  employeeId: string;         // 员工ID
  stationId: string;          // 兼职电站ID
  positionId: string;         // 兼职岗位ID
  startDate: Date;            // 开始日期
  endDate?: Date;             // 结束日期
  workload: number;           // 工作量百分比 (0-100)
  status: 'active' | 'inactive' | 'pending'; // 状态
  notes?: string;             // 备注
  createdAt?: Date;
  updatedAt?: Date;
}

export interface ConcurrentPositionDocument extends Omit<IConcurrentPosition, '_id' | 'employeeId' | 'stationId' | 'positionId'>, Document {
  employeeId: Types.ObjectId;
  stationId: Types.ObjectId;
  positionId: Types.ObjectId;
}

const ConcurrentPositionSchema = new Schema<ConcurrentPositionDocument>({
  employeeId: {
    type: Schema.Types.ObjectId,
    ref: 'Employee',
    required: true
  },
  stationId: {
    type: Schema.Types.ObjectId,
    ref: 'PowerStation',
    required: true
  },
  positionId: {
    type: Schema.Types.ObjectId,
    ref: 'Position',
    required: true
  },
  startDate: {
    type: Date,
    required: true
  },
  endDate: {
    type: Date
  },
  workload: {
    type: Number,
    required: true,
    min: 1,
    max: 100,
    default: 50
  },
  status: {
    type: String,
    required: true,
    enum: ['active', 'inactive', 'pending'],
    default: 'pending'
  },
  notes: {
    type: String,
    trim: true,
    maxlength: 500
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// 索引
ConcurrentPositionSchema.index({ employeeId: 1, stationId: 1, positionId: 1 }, { unique: true });
ConcurrentPositionSchema.index({ employeeId: 1 });
ConcurrentPositionSchema.index({ stationId: 1 });
ConcurrentPositionSchema.index({ positionId: 1 });
ConcurrentPositionSchema.index({ status: 1 });
ConcurrentPositionSchema.index({ startDate: 1, endDate: 1 });

// 虚拟字段
ConcurrentPositionSchema.virtual('id').get(function(this: any) {
  return this._id.toString();
});

// 检查是否在有效期内
ConcurrentPositionSchema.virtual('isActive').get(function(this: any) {
  const now = new Date();
  const isWithinDateRange = this.startDate <= now && (!this.endDate || this.endDate >= now);
  return this.status === 'active' && isWithinDateRange;
});

export const ConcurrentPosition = mongoose.model<ConcurrentPositionDocument>('ConcurrentPosition', ConcurrentPositionSchema);