import { Request, Response } from 'express';
import { PowerStation, PowerGeneration, Employee } from '../models';
import { StationPosition } from '../models/StationPosition';
import { ApiResponse, QueryParams, StationStats } from '../types';
import { asyncHandler } from '../middleware';

// 获取所有电站
export const getAllStations = asyncHandler(async (req: Request, res: Response): Promise<void> => {
  const { page = 1, limit = 10, search, filter } = req.query as QueryParams;

  const query: any = {};

  // 搜索功能
  if (search) {
    query.$or = [
      { name: { $regex: search, $options: 'i' } },
      { 'location.province': { $regex: search, $options: 'i' } },
      { 'location.city': { $regex: search, $options: 'i' } }
    ];
  }

  // 过滤功能
  if (filter) {
    if (filter.type) query.type = filter.type;
    if (filter.status) query.status = filter.status;
    if (filter.province) query['location.province'] = filter.province;
  }

  const skip = (Number(page) - 1) * Number(limit);
  
  const [stations, total] = await Promise.all([
    PowerStation.find(query)
      .skip(skip)
      .limit(Number(limit))
      .sort({ createdAt: -1 }),
    PowerStation.countDocuments(query)
  ]);

  const response: ApiResponse = {
    success: true,
    data: stations,
    pagination: {
      page: Number(page),
      limit: Number(limit),
      total,
      totalPages: Math.ceil(total / Number(limit))
    }
  };

  res.status(200).json(response);
});

// 获取电站的人员配置概览
export const getStationPersonnelOverview = asyncHandler(async (req: Request, res: Response): Promise<void> => {
  const { id } = req.params;

  // 获取电站岗位配置
  const stationPositions = await StationPosition.find({ stationId: id })
    .populate('positionId', 'positionName department level')
    .populate('employeeId', 'name employeeType');

  // 统计各部门的人员配置情况
  const departmentStats = stationPositions.reduce((acc: any, position: any) => {
    const dept = position.positionId.department;
    if (!acc[dept]) {
      acc[dept] = {
        totalPositions: 0,
        totalRequired: 0,
        totalAssigned: 0,
        positions: []
      };
    }
    
    acc[dept].totalPositions += 1;
    acc[dept].totalRequired += 1;
    acc[dept].totalAssigned += position.employeeId ? 1 : 0;
    acc[dept].positions.push({
      positionName: position.positionId.positionName,
      level: position.positionId.level,
      instance: position.instance,
      status: position.status,
      assigned: position.employeeId ? 1 : 0,
      employee: position.employeeId
    });
    
    return acc;
  }, {});

  const response: ApiResponse = {
    success: true,
    data: {
      stationId: id,
      departmentStats,
      totalPositions: stationPositions.length,
      totalAssigned: stationPositions.filter(pos => pos.employeeId).length
    }
  };

  res.status(200).json(response);
});

// 获取电站的岗位空缺情况
export const getStationVacancies = asyncHandler(async (req: Request, res: Response): Promise<void> => {
  const { id } = req.params;

  const stationPositions = await StationPosition.find({ stationId: id })
    .populate('positionId', 'positionName department level')
    .populate('employeeId', 'name employeeType');

  const vacancies = stationPositions
    .filter((position: any) => position.status === 'vacant' || !position.employeeId)
    .map((position: any) => ({
      positionId: position.positionId._id,
      positionName: position.positionId.positionName,
      department: position.positionId.department,
      level: position.positionId.level,
      instance: position.instance,
      status: position.status,
      assigned: position.employeeId ? 1 : 0,
      vacant: position.employeeId ? 0 : 1
    }));

  const response: ApiResponse = {
    success: true,
    data: {
      stationId: id,
      vacancies,
      totalVacant: vacancies.length
    }
  };

  res.status(200).json(response);
});

// 获取单个电站详情
export const getStationById = asyncHandler(async (req: Request, res: Response): Promise<void> => {
  const { id } = req.params;

  const station = await PowerStation.findById(id);

  if (!station) {
    res.status(404).json({
      success: false,
      message: '电站不存在'
    } as ApiResponse);
    return;
  }

  // 获取电站员工信息（当前在该电站工作的员工）
  const employees = await Employee.find({ currentStationId: id })
    .populate('homeStationId', 'name')
    .populate('currentStationId', 'name');

  // 获取电站岗位配置
  const stationPositions = await StationPosition.find({ stationId: id })
    .populate('positionId', 'positionName department level')
    .populate('employeeId', 'name employeeType');

  // 获取最新发电量数据
  const latestGeneration = await PowerGeneration.findOne({ stationId: id })
    .sort({ date: -1 });

  const response: ApiResponse = {
    success: true,
    data: {
      station,
      employees,
      stationPositions,
      latestGeneration
    }
  };

  res.status(200).json(response);
});

// 创建新电站
export const createStation = asyncHandler(async (req: Request, res: Response): Promise<void> => {
  const station = await PowerStation.create(req.body);

  const response: ApiResponse = {
    success: true,
    message: '电站创建成功',
    data: station
  };

  res.status(201).json(response);
});

// 更新电站信息
export const updateStation = asyncHandler(async (req: Request, res: Response): Promise<void> => {
  const { id } = req.params;

  const station = await PowerStation.findByIdAndUpdate(
    id,
    req.body,
    { new: true, runValidators: true }
  );

  if (!station) {
    res.status(404).json({
      success: false,
      message: '电站不存在'
    } as ApiResponse);
    return;
  }

  const response: ApiResponse = {
    success: true,
    message: '电站信息更新成功',
    data: station
  };

  res.status(200).json(response);
});

// 删除电站
export const deleteStation = asyncHandler(async (req: Request, res: Response): Promise<void> => {
  const { id } = req.params;

  const station = await PowerStation.findByIdAndDelete(id);

  if (!station) {
    res.status(404).json({
      success: false,
      message: '电站不存在'
    } as ApiResponse);
    return;
  }

  // 同时删除相关的员工、岗位配置和发电量数据
  await Promise.all([
    Employee.updateMany(
      { $or: [{ homeStationId: id }, { currentStationId: id }] },
      { $unset: { homeStationId: 1, currentStationId: 1 } }
    ),
    StationPosition.deleteMany({ stationId: id }),
    PowerGeneration.deleteMany({ stationId: id })
  ]);

  const response: ApiResponse = {
    success: true,
    message: '电站删除成功'
  };

  res.status(200).json(response);
});

// 获取电站统计数据
export const getStationStats = asyncHandler(async (req: Request, res: Response): Promise<void> => {
  const [
    totalStations,
    activeStations,
    totalCapacityResult,
    generationStats,
    efficiencyStats
  ] = await Promise.all([
    PowerStation.countDocuments(),
    PowerStation.countDocuments({ status: 'active' }),
    PowerStation.aggregate([
      { $group: { _id: null, totalCapacity: { $sum: '$capacity' } } }
    ]),
    PowerGeneration.aggregate([
      {
        $group: {
          _id: null,
          totalDaily: { $sum: '$dailyGeneration' },
          totalMonthly: { $sum: '$monthlyGeneration' },
          totalYearly: { $sum: '$yearlyGeneration' },
          totalCumulative: { $sum: '$totalGeneration' }
        }
      }
    ]),
    PowerGeneration.aggregate([
      {
        $group: {
          _id: null,
          avgEfficiency: { $avg: '$efficiency' }
        }
      }
    ])
  ]);

  const stats: StationStats = {
    totalStations,
    activeStations,
    totalCapacity: totalCapacityResult[0]?.totalCapacity || 0,
    totalGeneration: {
      daily: generationStats[0]?.totalDaily || 0,
      monthly: generationStats[0]?.totalMonthly || 0,
      yearly: generationStats[0]?.totalYearly || 0,
      total: generationStats[0]?.totalCumulative || 0
    },
    averageEfficiency: efficiencyStats[0]?.avgEfficiency || 0,
    alertCounts: {
      total: 0,
      critical: 0,
      high: 0,
      medium: 0,
      low: 0
    }
  };

  const response: ApiResponse = {
    success: true,
    data: stats
  };

  res.status(200).json(response);
});

// 获取电站发电量历史数据
export const getStationGenerationHistory = asyncHandler(async (req: Request, res: Response): Promise<void> => {
  const { id } = req.params;
  const { days = 30 } = req.query;

  const startDate = new Date();
  startDate.setDate(startDate.getDate() - Number(days));

  const generationHistory = await PowerGeneration.find({
    stationId: id,
    date: { $gte: startDate }
  }).sort({ date: 1 });

  const response: ApiResponse = {
    success: true,
    data: generationHistory
  };

  res.status(200).json(response);
});